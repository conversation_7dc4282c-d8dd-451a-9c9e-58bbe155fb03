using System.Diagnostics;

namespace EnglishAutomationApp.Helpers
{
    public static class PerformanceHelper
    {
        /// <summary>
        /// Bir işlemin süresini ölçer ve sonucu döndürür
        /// </summary>
        /// <typeparam name="T"><PERSON><PERSON><PERSON><PERSON><PERSON> tipi</typeparam>
        /// <param name="operation">Ölçülecek işlem</param>
        /// <param name="operationName">İşlem adı (log için)</param>
        /// <returns>İşlem sonucu ve süre bilgisi</returns>
        public static async Task<(T Result, TimeSpan Duration)> MeasureAsync<T>(
            Func<Task<T>> operation, 
            string operationName = "Operation")
        {
            var stopwatch = Stopwatch.StartNew();
            
            try
            {
                var result = await operation();
                stopwatch.Stop();
                
                LogPerformance(operationName, stopwatch.Elapsed);
                
                return (result, stopwatch.Elapsed);
            }
            catch (Exception ex)
            {
                stopwatch.Stop();
                LogError(operationName, stopwatch.Elapsed, ex);
                throw;
            }
        }

        /// <summary>
        /// Senkron işlem için performans ölçümü
        /// </summary>
        /// <typeparam name="T">Dönüş tipi</typeparam>
        /// <param name="operation">Ölçülecek işlem</param>
        /// <param name="operationName">İşlem adı</param>
        /// <returns>İşlem sonucu ve süre bilgisi</returns>
        public static (T Result, TimeSpan Duration) Measure<T>(
            Func<T> operation, 
            string operationName = "Operation")
        {
            var stopwatch = Stopwatch.StartNew();
            
            try
            {
                var result = operation();
                stopwatch.Stop();
                
                LogPerformance(operationName, stopwatch.Elapsed);
                
                return (result, stopwatch.Elapsed);
            }
            catch (Exception ex)
            {
                stopwatch.Stop();
                LogError(operationName, stopwatch.Elapsed, ex);
                throw;
            }
        }

        /// <summary>
        /// Void işlemler için performans ölçümü
        /// </summary>
        /// <param name="operation">Ölçülecek işlem</param>
        /// <param name="operationName">İşlem adı</param>
        /// <returns>İşlem süresi</returns>
        public static async Task<TimeSpan> MeasureAsync(
            Func<Task> operation, 
            string operationName = "Operation")
        {
            var stopwatch = Stopwatch.StartNew();
            
            try
            {
                await operation();
                stopwatch.Stop();
                
                LogPerformance(operationName, stopwatch.Elapsed);
                
                return stopwatch.Elapsed;
            }
            catch (Exception ex)
            {
                stopwatch.Stop();
                LogError(operationName, stopwatch.Elapsed, ex);
                throw;
            }
        }

        /// <summary>
        /// Bellek kullanımını ölçer
        /// </summary>
        /// <returns>Mevcut bellek kullanımı (MB)</returns>
        public static long GetMemoryUsageMB()
        {
            GC.Collect();
            GC.WaitForPendingFinalizers();
            GC.Collect();
            
            return GC.GetTotalMemory(false) / (1024 * 1024);
        }

        /// <summary>
        /// Performans loglaması
        /// </summary>
        /// <param name="operationName">İşlem adı</param>
        /// <param name="duration">Süre</param>
        private static void LogPerformance(string operationName, TimeSpan duration)
        {
            var memoryUsage = GetMemoryUsageMB();
            
            // Debug modunda konsola yaz
            Debug.WriteLine($"[PERFORMANCE] {operationName}: {duration.TotalMilliseconds:F2}ms | Memory: {memoryUsage}MB");
            
            // Yavaş işlemler için uyarı
            if (duration.TotalSeconds > 2)
            {
                Debug.WriteLine($"[WARNING] Slow operation detected: {operationName} took {duration.TotalSeconds:F2} seconds");
            }
        }

        /// <summary>
        /// Hata loglaması
        /// </summary>
        /// <param name="operationName">İşlem adı</param>
        /// <param name="duration">Süre</param>
        /// <param name="exception">Hata</param>
        private static void LogError(string operationName, TimeSpan duration, Exception exception)
        {
            Debug.WriteLine($"[ERROR] {operationName} failed after {duration.TotalMilliseconds:F2}ms: {exception.Message}");
        }

        /// <summary>
        /// Veritabanı bağlantı havuzu optimizasyonu için bağlantı sayısını kontrol eder
        /// </summary>
        /// <returns>Aktif bağlantı sayısı hakkında bilgi</returns>
        public static string GetDatabaseConnectionInfo()
        {
            try
            {
                // SQLite için basit bilgi
                return $"Database connections are managed by Entity Framework. Memory usage: {GetMemoryUsageMB()}MB";
            }
            catch (Exception ex)
            {
                return $"Could not retrieve connection info: {ex.Message}";
            }
        }

        /// <summary>
        /// Uygulama performans raporu oluşturur
        /// </summary>
        /// <returns>Performans raporu</returns>
        public static string GeneratePerformanceReport()
        {
            var report = new System.Text.StringBuilder();
            
            report.AppendLine("=== JAVA ÖĞRENME OTOMASYONU PERFORMANS RAPORU ===");
            report.AppendLine($"Rapor Tarihi: {DateTime.Now:dd.MM.yyyy HH:mm:ss}");
            report.AppendLine();
            
            // Bellek kullanımı
            var memoryUsage = GetMemoryUsageMB();
            report.AppendLine($"Bellek Kullanımı: {memoryUsage} MB");
            
            // İşlemci kullanımı (basit)
            var process = Process.GetCurrentProcess();
            report.AppendLine($"İşlemci Zamanı: {process.TotalProcessorTime.TotalSeconds:F2} saniye");
            report.AppendLine($"Çalışma Süresi: {(DateTime.Now - process.StartTime).TotalMinutes:F1} dakika");
            
            // Thread sayısı
            report.AppendLine($"Thread Sayısı: {process.Threads.Count}");
            
            // Veritabanı bilgisi
            report.AppendLine();
            report.AppendLine("Veritabanı Bilgisi:");
            report.AppendLine(GetDatabaseConnectionInfo());
            
            report.AppendLine();
            report.AppendLine("=== RAPOR SONU ===");
            
            return report.ToString();
        }
    }
}
