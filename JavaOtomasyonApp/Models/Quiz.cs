using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace EnglishAutomationApp.Models
{
    public enum QuestionType
    {
        MultipleChoice = 1,
        TrueFalse = 2,
        FillInTheBlank = 3,
        Matching = 4,
        Ordering = 5
    }

    public class Quiz
    {
        [Key]
        public int Id { get; set; }

        [Required]
        [ForeignKey("Lesson")]
        public int LessonId { get; set; }

        [Required]
        [MaxLength(200)]
        public string Title { get; set; } = string.Empty;

        [MaxLength(1000)]
        public string? Description { get; set; }

        [Range(1, 100)]
        public int PassingScore { get; set; } = 70;

        [Range(1, int.MaxValue)]
        public int TimeLimit { get; set; } = 10; // dakika

        [Required]
        public bool IsActive { get; set; } = true;

        [Required]
        public DateTime CreatedDate { get; set; } = DateTime.Now;

        public DateTime? UpdatedDate { get; set; }

        // Navigation Properties
        public virtual Lesson Lesson { get; set; } = null!;
        public virtual ICollection<Question> Questions { get; set; } = new List<Question>();
        public virtual ICollection<QuizAttempt> QuizAttempts { get; set; } = new List<QuizAttempt>();

        // Computed Properties
        public int QuestionCount => Questions.Count;
        public string FormattedTimeLimit => $"{TimeLimit} dakika";
    }

    public class Question
    {
        [Key]
        public int Id { get; set; }

        [Required]
        [ForeignKey("Quiz")]
        public int QuizId { get; set; }

        [Required]
        public string QuestionText { get; set; } = string.Empty;

        [Required]
        public QuestionType Type { get; set; }

        [Range(1, int.MaxValue)]
        public int OrderIndex { get; set; } = 1;

        [Range(1, 10)]
        public int Points { get; set; } = 1;

        [MaxLength(500)]
        public string? ImageUrl { get; set; }

        [MaxLength(500)]
        public string? AudioUrl { get; set; }

        [Required]
        public DateTime CreatedDate { get; set; } = DateTime.Now;

        // Navigation Properties
        public virtual Quiz Quiz { get; set; } = null!;
        public virtual ICollection<Answer> Answers { get; set; } = new List<Answer>();

        // Computed Properties
        public string TypeDisplayName => Type switch
        {
            QuestionType.MultipleChoice => "Çoktan Seçmeli",
            QuestionType.TrueFalse => "Doğru/Yanlış",
            QuestionType.FillInTheBlank => "Boşluk Doldurma",
            QuestionType.Matching => "Eşleştirme",
            QuestionType.Ordering => "Sıralama",
            _ => "Genel"
        };
    }

    public class Answer
    {
        [Key]
        public int Id { get; set; }

        [Required]
        [ForeignKey("Question")]
        public int QuestionId { get; set; }

        [Required]
        public string AnswerText { get; set; } = string.Empty;

        [Required]
        public bool IsCorrect { get; set; } = false;

        [Range(1, int.MaxValue)]
        public int OrderIndex { get; set; } = 1;

        [MaxLength(1000)]
        public string? Explanation { get; set; }

        // Navigation Properties
        public virtual Question Question { get; set; } = null!;
    }
}
