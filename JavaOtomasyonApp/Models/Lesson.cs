using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace JavaOtomasyonApp.Models
{
    public enum LessonType
    {
        Video = 1,
        Text = 2,
        Audio = 3,
        Interactive = 4,
        Quiz = 5
    }

    public class Lesson
    {
        [Key]
        public int Id { get; set; }

        [Required]
        [ForeignKey("Course")]
        public int CourseId { get; set; }

        [Required]
        [MaxLength(200)]
        public string Title { get; set; } = string.Empty;

        [Required]
        public string Content { get; set; } = string.Empty;

        [Required]
        public LessonType Type { get; set; }

        [Range(1, int.MaxValue)]
        public int OrderIndex { get; set; } = 1;

        public int EstimatedDurationMinutes { get; set; } = 15;

        [MaxLength(500)]
        public string? VideoUrl { get; set; }

        [MaxLength(500)]
        public string? AudioUrl { get; set; }

        [MaxLength(1000)]
        public string? Notes { get; set; }

        [Required]
        public bool IsActive { get; set; } = true;

        [Required]
        public DateTime CreatedDate { get; set; } = DateTime.Now;

        public DateTime? UpdatedDate { get; set; }

        // Navigation Properties
        public virtual Course Course { get; set; } = null!;
        public virtual ICollection<Quiz> Quizzes { get; set; } = new List<Quiz>();
        public virtual ICollection<LessonProgress> LessonProgresses { get; set; } = new List<LessonProgress>();

        // Computed Properties
        public string TypeDisplayName => Type switch
        {
            LessonType.Video => "Video",
            LessonType.Text => "Metin",
            LessonType.Audio => "Ses",
            LessonType.Interactive => "Etkileşimli",
            LessonType.Quiz => "Quiz",
            _ => "Genel"
        };

        public string EstimatedDuration => $"{EstimatedDurationMinutes} dakika";
    }
}
