using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace EnglishAutomationApp.Models
{
    public enum ProgressStatus
    {
        NotStarted = 1,
        InProgress = 2,
        Completed = 3
    }

    public class UserProgress
    {
        [Key]
        public int Id { get; set; }

        [Required]
        [ForeignKey("User")]
        public int UserId { get; set; }

        [Required]
        [ForeignKey("Course")]
        public int CourseId { get; set; }

        [Required]
        public ProgressStatus Status { get; set; } = ProgressStatus.NotStarted;

        [Range(0, 100)]
        public int ProgressPercentage { get; set; } = 0;

        public DateTime? StartedDate { get; set; }

        public DateTime? CompletedDate { get; set; }

        public DateTime? LastAccessedDate { get; set; }

        [Range(0, int.MaxValue)]
        public int TimeSpentMinutes { get; set; } = 0;

        [MaxLength(1000)]
        public string? Notes { get; set; }

        // Navigation Properties
        public virtual User User { get; set; } = null!;
        public virtual Course Course { get; set; } = null!;

        // Computed Properties
        public string StatusDisplayName => Status switch
        {
            ProgressStatus.NotStarted => "Başlanmadı",
            ProgressStatus.InProgress => "Devam Ediyor",
            ProgressStatus.Completed => "Tamamlandı",
            _ => "Bilinmiyor"
        };

        public string FormattedTimeSpent
        {
            get
            {
                if (TimeSpentMinutes < 60)
                    return $"{TimeSpentMinutes} dakika";
                
                var hours = TimeSpentMinutes / 60;
                var minutes = TimeSpentMinutes % 60;
                return minutes > 0 ? $"{hours} saat {minutes} dakika" : $"{hours} saat";
            }
        }

        public bool IsCompleted => Status == ProgressStatus.Completed;
        public bool HasStarted => Status != ProgressStatus.NotStarted;
    }
}
