using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace EnglishAutomationApp.Models
{
    public class LessonProgress
    {
        [Key]
        public int Id { get; set; }

        [Required]
        [ForeignKey("User")]
        public int UserId { get; set; }

        [Required]
        [ForeignKey("Lesson")]
        public int LessonId { get; set; }

        [Required]
        public ProgressStatus Status { get; set; } = ProgressStatus.NotStarted;

        [Range(0, 100)]
        public int ProgressPercentage { get; set; } = 0;

        public DateTime? StartedDate { get; set; }
        public DateTime? CompletedDate { get; set; }
        public DateTime? LastAccessedDate { get; set; }

        [Range(0, int.MaxValue)]
        public int TimeSpentMinutes { get; set; } = 0;

        [MaxLength(1000)]
        public string? Notes { get; set; }

        // Navigation Properties
        public virtual User User { get; set; } = null!;
        public virtual Lesson Lesson { get; set; } = null!;

        // Computed Properties
        public string StatusDisplayName => Status switch
        {
            ProgressStatus.NotStarted => "Başlanmadı",
            ProgressStatus.InProgress => "Devam Ediyor",
            ProgressStatus.Completed => "Tamamlandı",
            _ => "Bilinmiyor"
        };

        public string FormattedTimeSpent
        {
            get
            {
                if (TimeSpentMinutes < 60)
                    return $"{TimeSpentMinutes} dakika";
                
                var hours = TimeSpentMinutes / 60;
                var minutes = TimeSpentMinutes % 60;
                return minutes > 0 ? $"{hours} saat {minutes} dakika" : $"{hours} saat";
            }
        }

        public bool IsCompleted => Status == ProgressStatus.Completed;
        public bool HasStarted => Status != ProgressStatus.NotStarted;
    }

    public class QuizAttempt
    {
        [Key]
        public int Id { get; set; }

        [Required]
        [ForeignKey("User")]
        public int UserId { get; set; }

        [Required]
        [ForeignKey("Quiz")]
        public int QuizId { get; set; }

        [Range(0, 100)]
        public int Score { get; set; } = 0;

        [Range(0, int.MaxValue)]
        public int TimeSpentMinutes { get; set; } = 0;

        [Required]
        public bool IsCompleted { get; set; } = false;

        [Required]
        public DateTime StartedDate { get; set; } = DateTime.Now;

        public DateTime? CompletedDate { get; set; }

        // Navigation Properties
        public virtual User User { get; set; } = null!;
        public virtual Quiz Quiz { get; set; } = null!;
        public virtual ICollection<QuizAnswer> QuizAnswers { get; set; } = new List<QuizAnswer>();

        // Computed Properties
        public bool IsPassed => Quiz != null && Score >= Quiz.PassingScore;
        public string FormattedScore => $"{Score}/100";
    }

    public class QuizAnswer
    {
        [Key]
        public int Id { get; set; }

        [Required]
        [ForeignKey("QuizAttempt")]
        public int QuizAttemptId { get; set; }

        [Required]
        [ForeignKey("Question")]
        public int QuestionId { get; set; }

        [Required]
        public string UserAnswer { get; set; } = string.Empty;

        [Required]
        public bool IsCorrect { get; set; } = false;

        [Range(0, 10)]
        public int PointsEarned { get; set; } = 0;

        [Required]
        public DateTime AnsweredDate { get; set; } = DateTime.Now;

        // Navigation Properties
        public virtual QuizAttempt QuizAttempt { get; set; } = null!;
        public virtual Question Question { get; set; } = null!;
    }
}
