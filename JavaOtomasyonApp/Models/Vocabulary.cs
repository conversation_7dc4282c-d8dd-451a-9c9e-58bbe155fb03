using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace JavaOtomasyonApp.Models
{
    public enum WordDifficulty
    {
        Easy = 1,
        Medium = 2,
        Hard = 3
    }

    public enum PartOfSpeech
    {
        Noun = 1,
        Verb = 2,
        Adjective = 3,
        Adverb = 4,
        Preposition = 5,
        Conjunction = 6,
        Pronoun = 7,
        Interjection = 8
    }

    public class VocabularyWord
    {
        [Key]
        public int Id { get; set; }

        [Required]
        [MaxLength(100)]
        public string EnglishWord { get; set; } = string.Empty;

        [Required]
        [MaxLength(200)]
        public string TurkishMeaning { get; set; } = string.Empty;

        [MaxLength(200)]
        public string? Pronunciation { get; set; }

        [MaxLength(500)]
        public string? ExampleSentence { get; set; }

        [MaxLength(500)]
        public string? ExampleSentenceTurkish { get; set; }

        [Required]
        public WordDifficulty Difficulty { get; set; }

        [Required]
        public PartOfSpeech PartOfSpeech { get; set; }

        [MaxLength(100)]
        public string? Category { get; set; } // Örn: "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Seyahat"

        [MaxLength(500)]
        public string? AudioUrl { get; set; }

        [MaxLength(500)]
        public string? ImageUrl { get; set; }

        [Required]
        public bool IsActive { get; set; } = true;

        [Required]
        public DateTime CreatedDate { get; set; } = DateTime.Now;

        public DateTime? UpdatedDate { get; set; }

        // Navigation Properties
        public virtual ICollection<UserVocabulary> UserVocabularies { get; set; } = new List<UserVocabulary>();

        // Computed Properties
        public string DifficultyDisplayName => Difficulty switch
        {
            WordDifficulty.Easy => "Kolay",
            WordDifficulty.Medium => "Orta",
            WordDifficulty.Hard => "Zor",
            _ => "Bilinmiyor"
        };

        public string PartOfSpeechDisplayName => PartOfSpeech switch
        {
            PartOfSpeech.Noun => "İsim",
            PartOfSpeech.Verb => "Fiil",
            PartOfSpeech.Adjective => "Sıfat",
            PartOfSpeech.Adverb => "Zarf",
            PartOfSpeech.Preposition => "Edat",
            PartOfSpeech.Conjunction => "Bağlaç",
            PartOfSpeech.Pronoun => "Zamir",
            PartOfSpeech.Interjection => "Ünlem",
            _ => "Diğer"
        };
    }

    public class UserVocabulary
    {
        [Key]
        public int Id { get; set; }

        [Required]
        [ForeignKey("User")]
        public int UserId { get; set; }

        [Required]
        [ForeignKey("VocabularyWord")]
        public int VocabularyWordId { get; set; }

        [Range(0, 5)]
        public int MasteryLevel { get; set; } = 0; // 0: Bilinmiyor, 5: Tamamen öğrenildi

        public int CorrectAnswers { get; set; } = 0;
        public int TotalAttempts { get; set; } = 0;

        public DateTime? FirstLearnedDate { get; set; }
        public DateTime? LastReviewedDate { get; set; }

        [Required]
        public DateTime CreatedDate { get; set; } = DateTime.Now;

        // Navigation Properties
        public virtual User User { get; set; } = null!;
        public virtual VocabularyWord VocabularyWord { get; set; } = null!;

        // Computed Properties
        public double SuccessRate => TotalAttempts > 0 ? (double)CorrectAnswers / TotalAttempts * 100 : 0;
        public bool IsLearned => MasteryLevel >= 4;
        public bool NeedsReview => LastReviewedDate == null || LastReviewedDate < DateTime.Now.AddDays(-7);
    }
}
