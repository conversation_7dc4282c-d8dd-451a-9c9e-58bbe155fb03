using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace EnglishAutomationApp.Models
{
    public enum AchievementType
    {
        CourseCompletion = 1,
        QuizScore = 2,
        VocabularyMastery = 3,
        StudyStreak = 4,
        TimeSpent = 5,
        LessonCompletion = 6
    }

    public class Achievement
    {
        [Key]
        public int Id { get; set; }

        [Required]
        [MaxLength(100)]
        public string Name { get; set; } = string.Empty;

        [Required]
        [MaxLength(500)]
        public string Description { get; set; } = string.Empty;

        [Required]
        public AchievementType Type { get; set; }

        [MaxLength(100)]
        public string Icon { get; set; } = "🏆";

        [MaxLength(10)]
        public string Color { get; set; } = "#FFD700";

        [Range(1, int.MaxValue)]
        public int RequiredValue { get; set; } = 1; // Gerek<PERSON> değer (örn: 5 ders, 100 kelime)

        [Range(1, 100)]
        public int Points { get; set; } = 10; // Başarı puanı

        [Required]
        public bool IsActive { get; set; } = true;

        [Required]
        public DateTime CreatedDate { get; set; } = DateTime.Now;

        // Navigation Properties
        public virtual ICollection<UserAchievement> UserAchievements { get; set; } = new List<UserAchievement>();

        // Computed Properties
        public string TypeDisplayName => Type switch
        {
            AchievementType.CourseCompletion => "Kurs Tamamlama",
            AchievementType.QuizScore => "Quiz Başarısı",
            AchievementType.VocabularyMastery => "Kelime Hakimiyeti",
            AchievementType.StudyStreak => "Çalışma Serisi",
            AchievementType.TimeSpent => "Zaman Geçirme",
            AchievementType.LessonCompletion => "Ders Tamamlama",
            _ => "Genel"
        };
    }

    public class UserAchievement
    {
        [Key]
        public int Id { get; set; }

        [Required]
        [ForeignKey("User")]
        public int UserId { get; set; }

        [Required]
        [ForeignKey("Achievement")]
        public int AchievementId { get; set; }

        [Required]
        public DateTime EarnedDate { get; set; } = DateTime.Now;

        [Range(0, int.MaxValue)]
        public int CurrentValue { get; set; } = 0; // Mevcut ilerleme

        [Required]
        public bool IsCompleted { get; set; } = false;

        // Navigation Properties
        public virtual User User { get; set; } = null!;
        public virtual Achievement Achievement { get; set; } = null!;

        // Computed Properties
        public double ProgressPercentage => Achievement != null ? 
            Math.Min(100, (double)CurrentValue / Achievement.RequiredValue * 100) : 0;
    }
}
