<Window x:Class="JavaOtomasyonApp.Views.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="English Automation Platform"
        Height="700" Width="1200"
        WindowStartupLocation="CenterScreen"
        WindowState="Maximized">

    <Window.Background>
        <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
            <GradientStop Color="#667eea" Offset="0"/>
            <GradientStop Color="#764ba2" Offset="1"/>
        </LinearGradientBrush>
    </Window.Background>
    
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="250"/>
            <ColumnDefinition Width="*"/>
        </Grid.ColumnDefinitions>

        <!-- Top Menu Bar -->
        <Border Grid.Row="0" Grid.ColumnSpan="2" Padding="20,15">
            <Border.Background>
                <LinearGradientBrush StartPoint="0,0" EndPoint="1,0">
                    <GradientStop Color="#4facfe" Offset="0"/>
                    <GradientStop Color="#00f2fe" Offset="1"/>
                </LinearGradientBrush>
            </Border.Background>
            <Border.Effect>
                <DropShadowEffect Color="Black" Opacity="0.1" BlurRadius="10" ShadowDepth="2"/>
            </Border.Effect>

            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <StackPanel Grid.Column="0" Orientation="Horizontal" VerticalAlignment="Center">
                    <TextBlock Text="🌟" FontSize="28" Foreground="White" Margin="0,0,15,0"/>
                    <TextBlock Text="English Automation Platform"
                              FontSize="20" FontWeight="Bold" Foreground="White"
                              VerticalAlignment="Center"/>
                </StackPanel>

                <StackPanel Grid.Column="1" Orientation="Horizontal" VerticalAlignment="Center">
                    <TextBlock x:Name="WelcomeTextBlock" Text="Welcome, User!"
                              FontSize="14" Foreground="White" VerticalAlignment="Center" Margin="0,0,20,0"/>
                    
                    <Button x:Name="ProfileButton" Content="👤 Profile"
                           Background="Transparent" BorderThickness="0"
                           Foreground="White" FontWeight="SemiBold"
                           Cursor="Hand" Click="ProfileButton_Click" Margin="0,0,10,0"/>
                    
                    <Button x:Name="LogoutButton" Content="🚪 Logout"
                           Background="Transparent" BorderThickness="0"
                           Foreground="White" FontWeight="SemiBold"
                           Cursor="Hand" Click="LogoutButton_Click"/>
                </StackPanel>
            </Grid>
        </Border>

        <!-- Left Navigation Panel -->
        <Border Grid.Row="1" Grid.Column="0" Margin="20,0,10,20" CornerRadius="15">
            <Border.Background>
                <SolidColorBrush Color="White" Opacity="0.95"/>
            </Border.Background>
            <Border.Effect>
                <DropShadowEffect Color="Black" Opacity="0.1" BlurRadius="15" ShadowDepth="5"/>
            </Border.Effect>

            <StackPanel Margin="0,30,0,20">
                <TextBlock Text="Menu" FontSize="16" FontWeight="Bold"
                          Foreground="#333" Margin="20,0,20,20" HorizontalAlignment="Center"/>

                <Button x:Name="DashboardButton" Content="📊 Dashboard"
                       Style="{StaticResource ModernButtonStyle}" Click="DashboardButton_Click" Margin="10,5"/>

                <Button x:Name="CoursesButton" Content="📚 Courses"
                       Style="{StaticResource ModernButtonStyle}" Click="CoursesButton_Click" Margin="10,5"/>

                <Button x:Name="VocabularyButton" Content="📖 Vocabulary"
                       Style="{StaticResource ModernButtonStyle}" Click="VocabularyButton_Click" Margin="10,5"/>

                <Button x:Name="ProgressButton" Content="📈 Progress"
                       Style="{StaticResource ModernButtonStyle}" Click="ProgressButton_Click" Margin="10,5"/>

                <Button x:Name="AchievementsButton" Content="🏆 Achievements"
                       Style="{StaticResource ModernButtonStyle}" Click="AchievementsButton_Click" Margin="10,5"/>

                <Button x:Name="PaymentsButton" Content="💳 Payments"
                       Style="{StaticResource ModernButtonStyle}" Click="PaymentsButton_Click" Margin="10,5"/>

                <Rectangle Height="1" Fill="#E0E0E0" Margin="20,20"/>

                <Button x:Name="AdminButton" Content="⚙️ Admin Panel"
                       Style="{StaticResource ModernButtonStyle}" Click="AdminButton_Click"
                       Visibility="Collapsed" Margin="10,5"/>
            </StackPanel>
        </Border>

        <!-- Main Content Area -->
        <Border Grid.Row="1" Grid.Column="1" Margin="20,0,20,20" CornerRadius="15">
            <Border.Background>
                <SolidColorBrush Color="White" Opacity="0.95"/>
            </Border.Background>
            <Border.Effect>
                <DropShadowEffect Color="Black" Opacity="0.1" BlurRadius="15" ShadowDepth="5"/>
            </Border.Effect>

            <ContentControl x:Name="MainContent" Margin="20"/>
        </Border>

        <!-- Status Bar -->
        <Border Grid.Row="2" Grid.ColumnSpan="2" Padding="20,10" Margin="0,0,0,10">
            <Border.Background>
                <SolidColorBrush Color="White" Opacity="0.9"/>
            </Border.Background>
            <Border.Effect>
                <DropShadowEffect Color="Black" Opacity="0.05" BlurRadius="5" ShadowDepth="2"/>
            </Border.Effect>

            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <TextBlock x:Name="StatusTextBlock" Text="Ready"
                          FontSize="12" Foreground="#666" VerticalAlignment="Center"/>

                <TextBlock Grid.Column="1" Text="© 2024 English Automation Platform"
                          FontSize="11" Foreground="#888" VerticalAlignment="Center"/>
            </Grid>
        </Border>
    </Grid>
</Window>
