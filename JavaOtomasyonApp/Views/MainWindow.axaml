<Window xmlns="https://github.com/avaloniaui"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        x:Class="JavaOtomasyonApp.Views.MainWindow"
        Title="English Learning Platform"
        Height="700" Width="1200"
        WindowStartupLocation="CenterScreen"
        WindowState="Maximized">

    <Window.Background>
        <LinearGradientBrush StartPoint="0%,0%" EndPoint="100%,100%">
            <GradientStop Color="#667eea" Offset="0"/>
            <GradientStop Color="#764ba2" Offset="1"/>
        </LinearGradientBrush>
    </Window.Background>
    
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="250"/>
            <ColumnDefinition Width="*"/>
        </Grid.ColumnDefinitions>

        <!-- Top Menu Bar -->
        <Border Grid.Row="0" Grid.ColumnSpan="2" Padding="20,15">
            <Border.Background>
                <LinearGradientBrush StartPoint="0%,0%" EndPoint="100%,0%">
                    <GradientStop Color="#4facfe" Offset="0"/>
                    <GradientStop Color="#00f2fe" Offset="1"/>
                </LinearGradientBrush>
            </Border.Background>
            <Border.Effect>
                <DropShadowEffect Color="Black" Opacity="0.1" BlurRadius="10" OffsetY="2"/>
            </Border.Effect>

            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <StackPanel Grid.Column="0" Orientation="Horizontal" VerticalAlignment="Center">
                    <TextBlock Text="🌟" FontSize="28" Foreground="White" Margin="0,0,15,0"/>
                    <TextBlock Text="English Learning Platform"
                              FontSize="20" FontWeight="Bold" Foreground="White"
                              VerticalAlignment="Center"/>
                </StackPanel>

                <StackPanel Grid.Column="1" Orientation="Horizontal" VerticalAlignment="Center">
                    <TextBlock x:Name="WelcomeTextBlock"
                              Foreground="White" FontSize="14" FontWeight="Medium"
                              VerticalAlignment="Center" Margin="0,0,20,0"/>

                    <ComboBox x:Name="LanguageComboBox"
                             Background="Transparent" Foreground="White"
                             BorderBrush="White" BorderThickness="1"
                             Margin="0,0,15,0" Width="100"
                             SelectionChanged="LanguageComboBox_SelectionChanged"/>

                    <Button x:Name="ProfileButton" Content="👤 Profile"
                           Classes="modern-button-outline"
                           Margin="0,0,10,0"
                           Click="ProfileButton_Click"/>

                    <Button x:Name="LogoutButton" Content="🚪 Logout"
                           Classes="modern-button-danger"
                           Click="LogoutButton_Click"/>
                </StackPanel>
            </Grid>
        </Border>

        <!-- Left Navigation Panel -->
        <Border Grid.Row="1" Grid.Column="0" Margin="20,0,0,20" CornerRadius="15">
            <Border.Background>
                <SolidColorBrush Color="White" Opacity="0.95"/>
            </Border.Background>
            <Border.Effect>
                <DropShadowEffect Color="Black" Opacity="0.1" BlurRadius="15" OffsetY="5"/>
            </Border.Effect>

            <StackPanel Margin="0,30,0,20">
                <TextBlock Text="Menü" FontSize="16" FontWeight="Bold"
                          Foreground="#333" Margin="20,0,20,20" HorizontalAlignment="Center"/>

                <Button x:Name="DashboardButton" Content="📊 Dashboard"
                       Classes="nav-modern" Click="DashboardButton_Click"/>

                <Button x:Name="CoursesButton" Content="📚 Kurslar"
                       Classes="nav-modern" Click="CoursesButton_Click"/>

                <Button x:Name="VocabularyButton" Content="📖 Kelimeler"
                       Classes="nav-modern" Click="VocabularyButton_Click"/>

                <Button x:Name="ProgressButton" Content="📈 İlerleme"
                       Classes="nav-modern" Click="ProgressButton_Click"/>

                <Button x:Name="AchievementsButton" Content="🏆 Başarılar"
                       Classes="nav-modern" Click="AchievementsButton_Click"/>

                <Button x:Name="PaymentsButton" Content="💳 Ödemeler"
                       Classes="nav-modern" Click="PaymentsButton_Click"/>

                <Rectangle Height="1" Fill="#E0E0E0" Margin="20,20"/>

                <Button x:Name="AdminButton" Content="⚙️ Admin Panel"
                       Classes="nav-modern" Click="AdminButton_Click"
                       IsVisible="False"/>
            </StackPanel>
        </Border>

        <!-- Main Content Area -->
        <Border Grid.Row="1" Grid.Column="1" Margin="20,0,20,20" CornerRadius="15">
            <Border.Background>
                <SolidColorBrush Color="White" Opacity="0.95"/>
            </Border.Background>
            <Border.Effect>
                <DropShadowEffect Color="Black" Opacity="0.1" BlurRadius="15" OffsetY="5"/>
            </Border.Effect>

            <ContentControl x:Name="MainContent" Margin="20"/>
        </Border>

        <!-- Status Bar -->
        <Border Grid.Row="2" Grid.ColumnSpan="2" Padding="20,10" Margin="0,0,0,10">
            <Border.Background>
                <SolidColorBrush Color="White" Opacity="0.9"/>
            </Border.Background>
            <Border.Effect>
                <DropShadowEffect Color="Black" Opacity="0.05" BlurRadius="5" OffsetY="2"/>
            </Border.Effect>

            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <TextBlock x:Name="StatusTextBlock"
                          Text="Hazır" FontSize="12" FontWeight="Medium"
                          Foreground="#555" VerticalAlignment="Center"/>

                <TextBlock Grid.Column="1"
                          Text="© 2024 English Learning Platform"
                          FontSize="10" Foreground="#888"
                          VerticalAlignment="Center"/>
            </Grid>
        </Border>
    </Grid>

    <Window.Styles>
        <!-- Modern Navigation Button Style -->
        <Style Selector="Button.nav-modern">
            <Setter Property="Background" Value="Transparent"/>
            <Setter Property="Foreground" Value="#555"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="20,15"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontWeight" Value="Medium"/>
            <Setter Property="HorizontalAlignment" Value="Stretch"/>
            <Setter Property="HorizontalContentAlignment" Value="Left"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Margin" Value="10,2"/>
            <Setter Property="CornerRadius" Value="10"/>
            <Setter Property="Transitions">
                <Transitions>
                    <BrushTransition Property="Background" Duration="0:0:0.2"/>
                    <TransformOperationsTransition Property="RenderTransform" Duration="0:0:0.2"/>
                </Transitions>
            </Setter>
        </Style>

        <Style Selector="Button.nav-modern:pointerover">
            <Setter Property="Background">
                <Setter.Value>
                    <LinearGradientBrush StartPoint="0%,0%" EndPoint="100%,0%">
                        <GradientStop Color="#667eea" Offset="0"/>
                        <GradientStop Color="#764ba2" Offset="1"/>
                    </LinearGradientBrush>
                </Setter.Value>
            </Setter>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="RenderTransform" Value="scale(1.02)"/>
        </Style>

        <Style Selector="Button.nav-modern:pressed">
            <Setter Property="RenderTransform" Value="scale(0.98)"/>
        </Style>

        <!-- Modern Button Outline Style -->
        <Style Selector="Button.modern-button-outline">
            <Setter Property="Background" Value="Transparent"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderBrush" Value="White"/>
            <Setter Property="BorderThickness" Value="2"/>
            <Setter Property="Padding" Value="15,8"/>
            <Setter Property="FontSize" Value="13"/>
            <Setter Property="FontWeight" Value="Medium"/>
            <Setter Property="CornerRadius" Value="20"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Transitions">
                <Transitions>
                    <BrushTransition Property="Background" Duration="0:0:0.2"/>
                </Transitions>
            </Setter>
        </Style>

        <Style Selector="Button.modern-button-outline:pointerover">
            <Setter Property="Background" Value="White"/>
            <Setter Property="Foreground" Value="#667eea"/>
        </Style>

        <!-- Modern Button Danger Style -->
        <Style Selector="Button.modern-button-danger">
            <Setter Property="Background">
                <Setter.Value>
                    <LinearGradientBrush StartPoint="0%,0%" EndPoint="100%,0%">
                        <GradientStop Color="#ff6b6b" Offset="0"/>
                        <GradientStop Color="#ee5a52" Offset="1"/>
                    </LinearGradientBrush>
                </Setter.Value>
            </Setter>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="15,8"/>
            <Setter Property="FontSize" Value="13"/>
            <Setter Property="FontWeight" Value="Medium"/>
            <Setter Property="CornerRadius" Value="20"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Transitions">
                <Transitions>
                    <TransformOperationsTransition Property="RenderTransform" Duration="0:0:0.2"/>
                </Transitions>
            </Setter>
        </Style>

        <Style Selector="Button.modern-button-danger:pointerover">
            <Setter Property="RenderTransform" Value="scale(1.05)"/>
        </Style>

        <Style Selector="Button.modern-button-danger:pressed">
            <Setter Property="RenderTransform" Value="scale(0.95)"/>
        </Style>
    </Window.Styles>
</Window>
