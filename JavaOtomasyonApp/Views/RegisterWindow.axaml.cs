using System;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using JavaOtomasyonApp.Services;

namespace JavaOtomasyonApp.Views
{
    public partial class RegisterWindow : Window
    {
        public RegisterWindow()
        {
            InitializeComponent();
        }

        private async void RegisterButton_Click(object sender, RoutedEventArgs e)
        {
            if (!ValidateForm())
                return;

            RegisterButton.IsEnabled = false;
            RegisterButton.Content = "Creating account...";

            try
            {
                var result = await AuthenticationService.RegisterAsync(
                    EmailTextBox.Text!.Trim(),
                    PasswordBox.Password!,
                    FirstNameTextBox.Text!.Trim(),
                    LastNameTextBox.Text!.Trim());

                if (result.Success)
                {
                    ShowMessage(result.Message, true);

                    // Wait 2 seconds and close window
                    await Task.Delay(2000);
                    this.Close();
                }
                else
                {
                    ShowMessage(result.Message, false);
                }
            }
            catch (Exception ex)
            {
                ShowMessage($"Unexpected error: {ex.Message}", false);
            }
            finally
            {
                RegisterButton.IsEnabled = true;
                RegisterButton.Content = "Create Account";
            }
        }

        private void LoginButton_Click(object sender, RoutedEventArgs e)
        {
            this.Close();
        }

        private bool ValidateForm()
        {
            // First name check
            if (string.IsNullOrWhiteSpace(FirstNameTextBox.Text))
            {
                ShowMessage("Please enter your first name.", false);
                FirstNameTextBox.Focus();
                return false;
            }

            // Last name check
            if (string.IsNullOrWhiteSpace(LastNameTextBox.Text))
            {
                ShowMessage("Please enter your last name.", false);
                LastNameTextBox.Focus();
                return false;
            }

            // Email check
            if (string.IsNullOrWhiteSpace(EmailTextBox.Text))
            {
                ShowMessage("Please enter your email address.", false);
                EmailTextBox.Focus();
                return false;
            }

            if (!IsValidEmail(EmailTextBox.Text))
            {
                ShowMessage("Please enter a valid email address.", false);
                EmailTextBox.Focus();
                return false;
            }

            // Password check
            if (string.IsNullOrWhiteSpace(PasswordBox.Password))
            {
                ShowMessage("Please enter your password.", false);
                PasswordBox.Focus();
                return false;
            }

            if (PasswordBox.Password.Length < 6)
            {
                ShowMessage("Password must be at least 6 characters long.", false);
                PasswordBox.Focus();
                return false;
            }

            // Confirm password check
            if (PasswordBox.Password != ConfirmPasswordBox.Password)
            {
                ShowMessage("Passwords do not match.", false);
                ConfirmPasswordBox.Focus();
                return false;
            }

            return true;
        }

        private bool IsValidEmail(string email)
        {
            try
            {
                var emailRegex = new Regex(@"^[^@\s]+@[^@\s]+\.[^@\s]+$");
                return emailRegex.IsMatch(email);
            }
            catch
            {
                return false;
            }
        }

        private void ShowMessage(string message, bool isSuccess)
        {
            MessageTextBlock.Text = message;
            MessageTextBlock.Foreground = isSuccess ? 
                new SolidColorBrush(Colors.Green) : 
                new SolidColorBrush(Colors.Red);
        }
    }
}
