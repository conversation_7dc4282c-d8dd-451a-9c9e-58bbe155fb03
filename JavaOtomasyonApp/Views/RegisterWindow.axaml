<Window xmlns="https://github.com/avaloniaui"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        x:Class="JavaOtomasyonApp.Views.RegisterWindow"
        Title="Kayıt Ol - Java Öğrenme Otomasyonu"
        Height="600" Width="450"
        WindowStartupLocation="CenterOwner"
        CanResize="False"
        Background="#F5F5F5">
    
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" Background="#007ACC" Padding="20">
            <StackPanel HorizontalAlignment="Center">
                <TextBlock Text="☕" FontSize="48" HorizontalAlignment="Center" Foreground="White"/>
                <TextBlock Text="Yeni Hesap Oluştur" 
                          FontSize="18" FontWeight="Bold" 
                          HorizontalAlignment="Center" Foreground="White"/>
            </StackPanel>
        </Border>

        <!-- Register Form -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
            <Border Classes="card" Margin="20" VerticalAlignment="Center">
                <StackPanel Margin="30" VerticalAlignment="Center">
                    <TextBlock Text="Kayıt Formu" Classes="title" 
                              HorizontalAlignment="Center"/>

                    <TextBlock Text="Ad:" FontWeight="SemiBold" Margin="0,0,0,5"/>
                    <TextBox x:Name="FirstNameTextBox" Classes="modern"
                            Margin="0,0,0,15" Watermark="Adınızı girin"/>

                    <TextBlock Text="Soyad:" FontWeight="SemiBold" Margin="0,0,0,5"/>
                    <TextBox x:Name="LastNameTextBox" Classes="modern"
                            Margin="0,0,0,15" Watermark="Soyadınızı girin"/>

                    <TextBlock Text="E-posta:" FontWeight="SemiBold" Margin="0,0,0,5"/>
                    <TextBox x:Name="EmailTextBox" Classes="modern"
                            Margin="0,0,0,15" Watermark="E-posta adresinizi girin"/>

                    <TextBlock Text="Şifre:" FontWeight="SemiBold" Margin="0,0,0,5"/>
                    <TextBox x:Name="PasswordTextBox" Classes="modern"
                            PasswordChar="*" Margin="0,0,0,15" 
                            Watermark="Şifrenizi girin (en az 6 karakter)"/>

                    <TextBlock Text="Şifre Tekrar:" FontWeight="SemiBold" Margin="0,0,0,5"/>
                    <TextBox x:Name="ConfirmPasswordTextBox" Classes="modern"
                            PasswordChar="*" Margin="0,0,0,20" 
                            Watermark="Şifrenizi tekrar girin"/>

                    <CheckBox x:Name="TermsCheckBox" Margin="0,0,0,20">
                        <TextBlock TextWrapping="Wrap">
                            <Run Text="Kullanım şartlarını ve gizlilik politikasını okudum, kabul ediyorum."/>
                        </TextBlock>
                    </CheckBox>

                    <Button x:Name="RegisterButton" Content="Kayıt Ol" 
                           Classes="modern" Click="RegisterButton_Click" 
                           Margin="0,0,0,10"/>

                    <Button x:Name="CancelButton" Content="İptal" 
                           Background="#6C757D" Foreground="White"
                           BorderThickness="0" Padding="15,8" FontSize="14"
                           Cursor="Hand" Click="CancelButton_Click"/>

                    <TextBlock x:Name="MessageTextBlock" 
                              TextWrapping="Wrap" HorizontalAlignment="Center"
                              Margin="0,15,0,0" FontSize="12"/>
                </StackPanel>
            </Border>
        </ScrollViewer>

        <!-- Footer -->
        <Border Grid.Row="2" Background="#F0F0F0" Padding="10">
            <TextBlock Text="© 2024 Java Öğrenme Otomasyonu" 
                      HorizontalAlignment="Center" FontSize="10" 
                      Foreground="#666666"/>
        </Border>
    </Grid>
</Window>
