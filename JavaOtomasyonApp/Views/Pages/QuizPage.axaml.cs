using System.Collections.ObjectModel;
using System.Timers;
using Avalonia.Controls;
using Avalonia.Interactivity;
using Avalonia.Media;
using JavaOtomasyonApp.Data;
using JavaOtomasyonApp.Models;
using JavaOtomasyonApp.Services;
using Microsoft.EntityFrameworkCore;

namespace JavaOtomasyonApp.Views.Pages
{
    public partial class QuizPage : UserControl
    {
        public ObservableCollection<QuizViewModel> AvailableQuizzes { get; set; }
        
        private Quiz? _currentQuiz;
        private List<Question> _questions = new();
        private int _currentQuestionIndex = 0;
        private Dictionary<int, int> _userAnswers = new(); // QuestionId -> AnswerId
        private System.Timers.Timer? _timer;
        private TimeSpan _timeRemaining;
        private DateTime _quizStartTime;

        public QuizPage()
        {
            InitializeComponent();
            AvailableQuizzes = new ObservableCollection<QuizViewModel>();
            AvailableQuizzesItemsControl.ItemsSource = AvailableQuizzes;
            LoadAvailableQuizzes();
        }

        private async void LoadAvailableQuizzes()
        {
            try
            {
                using var context = new AppDbContext();
                var quizzes = await context.Quizzes
                    .Include(q => q.Lesson)
                    .ThenInclude(l => l.Course)
                    .Include(q => q.Questions)
                    .Where(q => q.IsActive)
                    .ToListAsync();

                AvailableQuizzes.Clear();
                foreach (var quiz in quizzes)
                {
                    AvailableQuizzes.Add(new QuizViewModel(quiz));
                }
            }
            catch (Exception ex)
            {
                // Handle error
            }
        }

        private async void StartQuizButton_Click(object? sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.Tag is int quizId)
            {
                await StartQuiz(quizId);
            }
        }

        private async Task StartQuiz(int quizId)
        {
            try
            {
                using var context = new AppDbContext();
                _currentQuiz = await context.Quizzes
                    .Include(q => q.Questions)
                    .ThenInclude(q => q.Answers)
                    .Include(q => q.Lesson)
                    .FirstOrDefaultAsync(q => q.Id == quizId);

                if (_currentQuiz == null) return;

                _questions = _currentQuiz.Questions.OrderBy(q => q.OrderIndex).ToList();
                _userAnswers.Clear();
                _currentQuestionIndex = 0;
                _quizStartTime = DateTime.Now;

                // Setup UI
                QuizSelectionCard.IsVisible = false;
                QuestionCard.IsVisible = true;
                ResultsCard.IsVisible = false;

                QuizTitleTextBlock.Text = _currentQuiz.Title;
                QuizDescriptionTextBlock.Text = _currentQuiz.Description ?? "";

                // Setup timer
                _timeRemaining = TimeSpan.FromMinutes(_currentQuiz.TimeLimit);
                StartTimer();

                ShowCurrentQuestion();
            }
            catch (Exception ex)
            {
                // Handle error
            }
        }

        private void StartTimer()
        {
            _timer?.Stop();
            _timer = new System.Timers.Timer(1000); // Update every second
            _timer.Elapsed += Timer_Elapsed;
            _timer.Start();
        }

        private void Timer_Elapsed(object? sender, ElapsedEventArgs e)
        {
            _timeRemaining = _timeRemaining.Subtract(TimeSpan.FromSeconds(1));
            
            Avalonia.Threading.Dispatcher.UIThread.InvokeAsync(() =>
            {
                TimerTextBlock.Text = $"{_timeRemaining.Minutes:D2}:{_timeRemaining.Seconds:D2}";
                
                if (_timeRemaining.TotalSeconds <= 0)
                {
                    _timer?.Stop();
                    FinishQuiz();
                }
            });
        }

        private void ShowCurrentQuestion()
        {
            if (_currentQuestionIndex >= _questions.Count) return;

            var question = _questions[_currentQuestionIndex];
            
            QuestionNumberTextBlock.Text = $"Question {_currentQuestionIndex + 1}";
            QuestionTextBlock.Text = question.QuestionText;
            ProgressTextBlock.Text = $"{_currentQuestionIndex + 1}/{_questions.Count}";
            
            // Update progress bar
            var progressPercentage = (double)(_currentQuestionIndex + 1) / _questions.Count * 100;
            ProgressPercentageTextBlock.Text = $"{progressPercentage:F0}%";
            ProgressBar.Width = progressPercentage * 3; // Adjust based on container width

            // Clear previous answers
            AnswerOptionsPanel.Children.Clear();

            // Add answer options
            foreach (var answer in question.Answers.OrderBy(a => a.OrderIndex))
            {
                var answerButton = new RadioButton
                {
                    Content = answer.AnswerText,
                    GroupName = $"Question{question.Id}",
                    Tag = answer.Id,
                    FontSize = 14,
                    Margin = new Avalonia.Thickness(0, 5),
                    Padding = new Avalonia.Thickness(15, 10)
                };

                answerButton.IsCheckedChanged += AnswerButton_IsCheckedChanged;
                
                // Restore previous selection if any
                if (_userAnswers.ContainsKey(question.Id) && _userAnswers[question.Id] == answer.Id)
                {
                    answerButton.IsChecked = true;
                }

                AnswerOptionsPanel.Children.Add(answerButton);
            }

            // Update navigation buttons
            PreviousButton.IsEnabled = _currentQuestionIndex > 0;
            NextButton.IsEnabled = _userAnswers.ContainsKey(question.Id);
            FinishButton.IsVisible = _currentQuestionIndex == _questions.Count - 1;
        }

        private void AnswerButton_IsCheckedChanged(object? sender, RoutedEventArgs e)
        {
            if (sender is RadioButton radioButton && radioButton.Tag is int answerId && radioButton.IsChecked == true)
            {
                var question = _questions[_currentQuestionIndex];
                _userAnswers[question.Id] = answerId;
                NextButton.IsEnabled = true;
            }
        }

        private void PreviousButton_Click(object? sender, RoutedEventArgs e)
        {
            if (_currentQuestionIndex > 0)
            {
                _currentQuestionIndex--;
                ShowCurrentQuestion();
            }
        }

        private void NextButton_Click(object? sender, RoutedEventArgs e)
        {
            if (_currentQuestionIndex < _questions.Count - 1)
            {
                _currentQuestionIndex++;
                ShowCurrentQuestion();
            }
        }

        private void FinishButton_Click(object? sender, RoutedEventArgs e)
        {
            FinishQuiz();
        }

        private async void FinishQuiz()
        {
            _timer?.Stop();

            if (_currentQuiz == null) return;

            try
            {
                // Calculate results
                int correctAnswers = 0;
                int totalQuestions = _questions.Count;

                foreach (var question in _questions)
                {
                    if (_userAnswers.TryGetValue(question.Id, out var userAnswerId))
                    {
                        var correctAnswer = question.Answers.FirstOrDefault(a => a.IsCorrect);
                        if (correctAnswer != null && correctAnswer.Id == userAnswerId)
                        {
                            correctAnswers++;
                        }
                    }
                }

                var score = totalQuestions > 0 ? (int)((double)correctAnswers / totalQuestions * 100) : 0;
                var timeSpent = DateTime.Now - _quizStartTime;

                // Save quiz attempt
                if (AuthenticationService.CurrentUser != null)
                {
                    await SaveQuizAttempt(score, timeSpent);
                }

                // Show results
                ShowResults(score, correctAnswers, totalQuestions, timeSpent);
            }
            catch (Exception ex)
            {
                // Handle error
            }
        }

        private async Task SaveQuizAttempt(int score, TimeSpan timeSpent)
        {
            if (_currentQuiz == null || AuthenticationService.CurrentUser == null) return;

            try
            {
                using var context = new AppDbContext();
                
                var quizAttempt = new QuizAttempt
                {
                    UserId = AuthenticationService.CurrentUser.Id,
                    QuizId = _currentQuiz.Id,
                    Score = score,
                    TimeSpentMinutes = (int)timeSpent.TotalMinutes,
                    IsCompleted = true,
                    StartedDate = _quizStartTime,
                    CompletedDate = DateTime.Now
                };

                context.QuizAttempts.Add(quizAttempt);
                await context.SaveChangesAsync();

                // Save individual answers
                foreach (var userAnswer in _userAnswers)
                {
                    var question = _questions.FirstOrDefault(q => q.Id == userAnswer.Key);
                    if (question != null)
                    {
                        var correctAnswer = question.Answers.FirstOrDefault(a => a.IsCorrect);
                        var isCorrect = correctAnswer != null && correctAnswer.Id == userAnswer.Value;

                        var quizAnswer = new QuizAnswer
                        {
                            QuizAttemptId = quizAttempt.Id,
                            QuestionId = userAnswer.Key,
                            UserAnswer = userAnswer.Value.ToString(),
                            IsCorrect = isCorrect,
                            PointsEarned = isCorrect ? question.Points : 0
                        };

                        context.QuizAnswers.Add(quizAnswer);
                    }
                }

                await context.SaveChangesAsync();
            }
            catch (Exception ex)
            {
                // Handle error
            }
        }

        private void ShowResults(int score, int correctAnswers, int totalQuestions, TimeSpan timeSpent)
        {
            QuestionCard.IsVisible = false;
            ResultsCard.IsVisible = true;

            FinalScoreTextBlock.Text = $"{score}%";
            CorrectAnswersTextBlock.Text = $"{correctAnswers}/{totalQuestions}";
            TimeSpentTextBlock.Text = $"{timeSpent.Minutes}:{timeSpent.Seconds:D2}";

            // Update score color based on performance
            var scoreColor = score >= 80 ? "#4CAF50" : score >= 60 ? "#FF9800" : "#F44336";
            FinalScoreTextBlock.Foreground = Brush.Parse(scoreColor);
        }

        private void ReviewAnswersButton_Click(object? sender, RoutedEventArgs e)
        {
            // TODO: Implement answer review
        }

        private void RetakeQuizButton_Click(object? sender, RoutedEventArgs e)
        {
            if (_currentQuiz != null)
            {
                _ = StartQuiz(_currentQuiz.Id);
            }
        }

        private void BackToMenuButton_Click(object? sender, RoutedEventArgs e)
        {
            _timer?.Stop();
            QuestionCard.IsVisible = false;
            ResultsCard.IsVisible = false;
            QuizSelectionCard.IsVisible = true;
        }
    }

    public class QuizViewModel
    {
        public QuizViewModel(Quiz quiz)
        {
            Id = quiz.Id;
            Title = quiz.Title;
            Description = quiz.Description ?? "";
            QuestionCount = quiz.Questions?.Count ?? 0;
            TimeLimit = quiz.TimeLimit;
            PassingScore = quiz.PassingScore;
            CategoryIcon = GetCategoryIcon(quiz.Lesson?.Course?.Type ?? CourseType.Grammar);
            CategoryColor = quiz.Lesson?.Course?.Color ?? "#667eea";
        }

        public int Id { get; set; }
        public string Title { get; set; }
        public string Description { get; set; }
        public int QuestionCount { get; set; }
        public int TimeLimit { get; set; }
        public int PassingScore { get; set; }
        public string CategoryIcon { get; set; }
        public string CategoryColor { get; set; }

        public string QuestionCountText => $"{QuestionCount} Questions";
        public string TimeLimitText => $"{TimeLimit} min";
        public string DifficultyText => PassingScore >= 80 ? "Hard" : PassingScore >= 60 ? "Medium" : "Easy";
        public string DifficultyColor => PassingScore >= 80 ? "#F44336" : PassingScore >= 60 ? "#FF9800" : "#4CAF50";

        private string GetCategoryIcon(CourseType type)
        {
            return type switch
            {
                CourseType.Grammar => "📝",
                CourseType.Vocabulary => "📖",
                CourseType.Speaking => "🗣️",
                CourseType.Listening => "👂",
                CourseType.Reading => "📚",
                CourseType.Writing => "✍️",
                CourseType.Pronunciation => "🔊",
                _ => "🧠"
            };
        }
    }
}
