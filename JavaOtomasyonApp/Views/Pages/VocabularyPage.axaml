<UserControl xmlns="https://github.com/avaloniaui"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:pages="using:JavaOtomasyonApp.Views.Pages"
             x:Class="JavaOtomasyonApp.Views.Pages.VocabularyPage">

    <ScrollViewer VerticalScrollBarVisibility="Auto">
        <StackPanel Margin="20">
            <TextBlock Text="📖 Vocabulary Learning" Classes="title"/>
            <TextBlock Text="Expand your English vocabulary with interactive word learning!" 
                      FontSize="16" Foreground="#666666" TextWrapping="Wrap" Margin="0,0,0,30"/>

            <!-- Study Mode Selection -->
            <Border Classes="modern-card" Margin="0,0,0,20">
                <StackPanel>
                    <TextBlock Text="Study Mode 🎯" Classes="modern-subtitle" Margin="0,0,0,15"/>
                    <StackPanel Orientation="Horizontal" Spacing="15">
                        <Button x:Name="FlashcardsButton" Content="📚 Flashcards" Classes="action-button-primary" Click="FlashcardsButton_Click"/>
                        <Button x:Name="QuizButton" Content="🧠 Quiz Mode" Classes="action-button-success" Click="QuizButton_Click"/>
                        <Button x:Name="ReviewButton" Content="🔄 Review Words" Classes="action-button-info" Click="ReviewButton_Click"/>
                    </StackPanel>
                </StackPanel>
            </Border>

            <!-- Word Categories -->
            <Border Classes="modern-card" Margin="0,0,0,20">
                <StackPanel>
                    <TextBlock Text="Categories 📂" Classes="modern-subtitle" Margin="0,0,0,15"/>
                    <ItemsControl x:Name="CategoriesItemsControl">
                        <ItemsControl.ItemsPanel>
                            <ItemsPanelTemplate>
                                <WrapPanel Orientation="Horizontal"/>
                            </ItemsPanelTemplate>
                        </ItemsControl.ItemsPanel>
                        <ItemsControl.ItemTemplate>
                            <DataTemplate x:DataType="pages:CategoryViewModel">
                                <Border Classes="category-chip" Margin="5" Cursor="Hand">
                                    <StackPanel Orientation="Horizontal" Spacing="8">
                                        <TextBlock Text="{Binding Icon}" FontSize="16"/>
                                        <TextBlock Text="{Binding Name}" FontSize="14" FontWeight="Medium"/>
                                        <Border Background="#E3F2FD" CornerRadius="10" Padding="6,2">
                                            <TextBlock Text="{Binding WordCount}" FontSize="11" Foreground="#1976D2"/>
                                        </Border>
                                    </StackPanel>
                                </Border>
                            </DataTemplate>
                        </ItemsControl.ItemTemplate>
                    </ItemsControl>
                </StackPanel>
            </Border>

            <!-- Progress Overview -->
            <Border Classes="modern-card" Margin="0,0,0,20">
                <StackPanel>
                    <TextBlock Text="Your Progress 📈" Classes="modern-subtitle" Margin="0,0,0,15"/>
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>

                        <StackPanel Grid.Column="0" HorizontalAlignment="Center">
                            <TextBlock Text="📚" FontSize="32" HorizontalAlignment="Center"/>
                            <TextBlock x:Name="TotalWordsTextBlock" Text="0" FontSize="24" FontWeight="Bold" 
                                      HorizontalAlignment="Center" Foreground="#667eea"/>
                            <TextBlock Text="Total Words" FontSize="12" HorizontalAlignment="Center" Foreground="#666"/>
                        </StackPanel>

                        <StackPanel Grid.Column="1" HorizontalAlignment="Center">
                            <TextBlock Text="✅" FontSize="32" HorizontalAlignment="Center"/>
                            <TextBlock x:Name="LearnedWordsTextBlock" Text="0" FontSize="24" FontWeight="Bold" 
                                      HorizontalAlignment="Center" Foreground="#4CAF50"/>
                            <TextBlock Text="Learned" FontSize="12" HorizontalAlignment="Center" Foreground="#666"/>
                        </StackPanel>

                        <StackPanel Grid.Column="2" HorizontalAlignment="Center">
                            <TextBlock Text="🔄" FontSize="32" HorizontalAlignment="Center"/>
                            <TextBlock x:Name="ReviewWordsTextBlock" Text="0" FontSize="24" FontWeight="Bold" 
                                      HorizontalAlignment="Center" Foreground="#FF9800"/>
                            <TextBlock Text="Need Review" FontSize="12" HorizontalAlignment="Center" Foreground="#666"/>
                        </StackPanel>

                        <StackPanel Grid.Column="3" HorizontalAlignment="Center">
                            <TextBlock Text="🎯" FontSize="32" HorizontalAlignment="Center"/>
                            <TextBlock x:Name="AccuracyTextBlock" Text="0%" FontSize="24" FontWeight="Bold" 
                                      HorizontalAlignment="Center" Foreground="#9C27B0"/>
                            <TextBlock Text="Accuracy" FontSize="12" HorizontalAlignment="Center" Foreground="#666"/>
                        </StackPanel>
                    </Grid>
                </StackPanel>
            </Border>

            <!-- Recent Words -->
            <Border Classes="modern-card">
                <StackPanel>
                    <TextBlock Text="Recent Words 🆕" Classes="modern-subtitle" Margin="0,0,0,15"/>
                    <ItemsControl x:Name="RecentWordsItemsControl">
                        <ItemsControl.ItemTemplate>
                            <DataTemplate x:DataType="pages:VocabularyWordViewModel">
                                <Border Classes="word-card" Margin="0,8">
                                    <Grid>
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="Auto"/>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="Auto"/>
                                        </Grid.ColumnDefinitions>

                                        <Border Grid.Column="0" Background="#F0F8FF" CornerRadius="25" 
                                               Width="50" Height="50" VerticalAlignment="Center" Margin="0,0,15,0">
                                            <TextBlock Text="{Binding MasteryIcon}" FontSize="24" 
                                                      HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                        </Border>

                                        <StackPanel Grid.Column="1" VerticalAlignment="Center">
                                            <TextBlock Text="{Binding EnglishWord}" FontSize="16" FontWeight="Bold"/>
                                            <TextBlock Text="{Binding TurkishMeaning}" FontSize="14" Foreground="#666" Margin="0,2,0,0"/>
                                            <TextBlock Text="{Binding Pronunciation}" FontSize="12" Foreground="#888" Margin="0,2,0,0"/>
                                        </StackPanel>

                                        <StackPanel Grid.Column="2" VerticalAlignment="Center" Spacing="5">
                                            <Border Background="{Binding DifficultyColor}" CornerRadius="10" Padding="8,4">
                                                <TextBlock Text="{Binding DifficultyDisplayName}" FontSize="11" 
                                                          Foreground="White" FontWeight="Medium"/>
                                            </Border>
                                            <Button Content="🔊" Classes="icon-button" Click="PlayPronunciationButton_Click" 
                                                   Tag="{Binding Id}"/>
                                        </StackPanel>
                                    </Grid>
                                </Border>
                            </DataTemplate>
                        </ItemsControl.ItemTemplate>
                    </ItemsControl>
                </StackPanel>
            </Border>
        </StackPanel>
    </ScrollViewer>
</UserControl>
