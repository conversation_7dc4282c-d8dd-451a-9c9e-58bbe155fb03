using System.Collections.ObjectModel;
using Avalonia.Controls;
using Avalonia.Interactivity;
using JavaOtomasyonApp.Data;
using JavaOtomasyonApp.Models;
using JavaOtomasyonApp.Services;
using Microsoft.EntityFrameworkCore;

namespace JavaOtomasyonApp.Views.Pages
{
    public partial class VocabularyPage : UserControl
    {
        public ObservableCollection<CategoryViewModel> Categories { get; set; }
        public ObservableCollection<VocabularyWordViewModel> RecentWords { get; set; }

        public VocabularyPage()
        {
            InitializeComponent();
            Categories = new ObservableCollection<CategoryViewModel>();
            RecentWords = new ObservableCollection<VocabularyWordViewModel>();
            
            CategoriesItemsControl.ItemsSource = Categories;
            RecentWordsItemsControl.ItemsSource = RecentWords;
            
            LoadVocabularyData();
        }

        private async void LoadVocabularyData()
        {
            try
            {
                if (AuthenticationService.CurrentUser == null) return;

                using var context = new AppDbContext();
                var userId = AuthenticationService.CurrentUser.Id;

                // Load categories
                var categories = await context.VocabularyWords
                    .Where(vw => vw.IsActive)
                    .GroupBy(vw => vw.Category)
                    .Select(g => new { Category = g.Key, Count = g.Count() })
                    .ToListAsync();

                Categories.Clear();
                foreach (var category in categories)
                {
                    Categories.Add(new CategoryViewModel
                    {
                        Name = category.Category ?? "General",
                        WordCount = category.Count,
                        Icon = GetCategoryIcon(category.Category ?? "General")
                    });
                }

                // Load user progress
                var userVocabularies = await context.UserVocabularies
                    .Where(uv => uv.UserId == userId)
                    .Include(uv => uv.VocabularyWord)
                    .ToListAsync();

                var totalWords = await context.VocabularyWords.CountAsync(vw => vw.IsActive);
                var learnedWords = userVocabularies.Count(uv => uv.IsLearned);
                var reviewWords = userVocabularies.Count(uv => uv.NeedsReview);
                var accuracy = userVocabularies.Any() ? userVocabularies.Average(uv => uv.SuccessRate) : 0;

                TotalWordsTextBlock.Text = totalWords.ToString();
                LearnedWordsTextBlock.Text = learnedWords.ToString();
                ReviewWordsTextBlock.Text = reviewWords.ToString();
                AccuracyTextBlock.Text = $"{accuracy:F0}%";

                // Load recent words
                var recentWords = await context.VocabularyWords
                    .Where(vw => vw.IsActive)
                    .OrderBy(vw => vw.Id)
                    .Take(10)
                    .ToListAsync();

                RecentWords.Clear();
                foreach (var word in recentWords)
                {
                    var userVocab = userVocabularies.FirstOrDefault(uv => uv.VocabularyWordId == word.Id);
                    RecentWords.Add(new VocabularyWordViewModel(word, userVocab));
                }
            }
            catch (Exception ex)
            {
                // Handle error
            }
        }

        private string GetCategoryIcon(string category)
        {
            return category.ToLower() switch
            {
                "family" => "👨‍👩‍👧‍👦",
                "colors" => "🎨",
                "food" => "🍎",
                "drinks" => "🥤",
                "animals" => "🐕",
                "body" => "👤",
                "clothes" => "👕",
                "house" => "🏠",
                "work" => "💼",
                "travel" => "✈️",
                _ => "📚"
            };
        }

        private void FlashcardsButton_Click(object? sender, RoutedEventArgs e)
        {
            // TODO: Navigate to flashcards mode
        }

        private void QuizButton_Click(object? sender, RoutedEventArgs e)
        {
            // Navigate to quiz page
            var mainWindow = TopLevel.GetTopLevel(this) as MainWindow;
            mainWindow?.NavigateToPage(new QuizPage());
        }

        private void ReviewButton_Click(object? sender, RoutedEventArgs e)
        {
            // TODO: Navigate to review mode
        }

        private async void PlayPronunciationButton_Click(object? sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.Tag is int wordId)
            {
                try
                {
                    using var context = new AppDbContext();
                    var word = await context.VocabularyWords.FindAsync(wordId);
                    if (word != null)
                    {
                        await AudioService.PlayPronunciationAsync(word.EnglishWord);
                    }
                }
                catch (Exception ex)
                {
                    // Handle error silently
                }
            }
        }
    }

    public class CategoryViewModel
    {
        public string Name { get; set; } = string.Empty;
        public int WordCount { get; set; }
        public string Icon { get; set; } = "📚";
    }

    public class VocabularyWordViewModel
    {
        public VocabularyWordViewModel(VocabularyWord word, UserVocabulary? userVocab = null)
        {
            Id = word.Id;
            EnglishWord = word.EnglishWord;
            TurkishMeaning = word.TurkishMeaning;
            Pronunciation = word.Pronunciation ?? "";
            Difficulty = word.Difficulty;
            DifficultyDisplayName = word.DifficultyDisplayName;
            MasteryLevel = userVocab?.MasteryLevel ?? 0;
        }

        public int Id { get; set; }
        public string EnglishWord { get; set; }
        public string TurkishMeaning { get; set; }
        public string Pronunciation { get; set; }
        public WordDifficulty Difficulty { get; set; }
        public string DifficultyDisplayName { get; set; }
        public int MasteryLevel { get; set; }

        public string MasteryIcon => MasteryLevel switch
        {
            0 => "❓",
            1 => "🔴",
            2 => "🟡",
            3 => "🟢",
            4 => "🔵",
            5 => "⭐",
            _ => "❓"
        };

        public string DifficultyColor => Difficulty switch
        {
            WordDifficulty.Easy => "#4CAF50",
            WordDifficulty.Medium => "#FF9800",
            WordDifficulty.Hard => "#F44336",
            _ => "#666666"
        };
    }
}
