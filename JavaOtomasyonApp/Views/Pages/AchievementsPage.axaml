<UserControl xmlns="https://github.com/avaloniaui"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:pages="using:JavaOtomasyonApp.Views.Pages"
             x:Class="JavaOtomasyonApp.Views.Pages.AchievementsPage">

    <ScrollViewer VerticalScrollBarVisibility="Auto">
        <StackPanel Margin="20">
            <TextBlock Text="🏆 Achievements" Classes="title"/>
            <TextBlock Text="Track your learning progress and unlock achievements!" 
                      FontSize="16" Foreground="#666666" TextWrapping="Wrap" Margin="0,0,0,30"/>

            <!-- Progress Summary -->
            <Border Classes="modern-card" Margin="0,0,0,20">
                <StackPanel>
                    <TextBlock Text="Your Progress 📊" Classes="modern-subtitle" Margin="0,0,0,15"/>
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>

                        <StackPanel Grid.Column="0" HorizontalAlignment="Center">
                            <TextBlock Text="🏆" FontSize="36" HorizontalAlignment="Center"/>
                            <TextBlock x:Name="EarnedAchievementsTextBlock" Text="0" FontSize="28" FontWeight="Bold" 
                                      HorizontalAlignment="Center" Foreground="#FFD700"/>
                            <TextBlock Text="Earned" FontSize="13" HorizontalAlignment="Center" Foreground="#666"/>
                        </StackPanel>

                        <StackPanel Grid.Column="1" HorizontalAlignment="Center">
                            <TextBlock Text="⭐" FontSize="36" HorizontalAlignment="Center"/>
                            <TextBlock x:Name="TotalPointsTextBlock" Text="0" FontSize="28" FontWeight="Bold" 
                                      HorizontalAlignment="Center" Foreground="#667eea"/>
                            <TextBlock Text="Total Points" FontSize="13" HorizontalAlignment="Center" Foreground="#666"/>
                        </StackPanel>

                        <StackPanel Grid.Column="2" HorizontalAlignment="Center">
                            <TextBlock Text="📈" FontSize="36" HorizontalAlignment="Center"/>
                            <TextBlock x:Name="CompletionRateTextBlock" Text="0%" FontSize="28" FontWeight="Bold" 
                                      HorizontalAlignment="Center" Foreground="#4CAF50"/>
                            <TextBlock Text="Completion" FontSize="13" HorizontalAlignment="Center" Foreground="#666"/>
                        </StackPanel>
                    </Grid>
                </StackPanel>
            </Border>

            <!-- Achievement Categories -->
            <Border Classes="modern-card" Margin="0,0,0,20">
                <StackPanel>
                    <TextBlock Text="Categories 📂" Classes="modern-subtitle" Margin="0,0,0,15"/>
                    <StackPanel Orientation="Horizontal" Spacing="10">
                        <Button x:Name="AllCategoriesButton" Content="All" Classes="category-filter-button" 
                               Click="CategoryFilterButton_Click" Tag="All"/>
                        <Button x:Name="CourseButton" Content="📚 Courses" Classes="category-filter-button" 
                               Click="CategoryFilterButton_Click" Tag="CourseCompletion"/>
                        <Button x:Name="VocabularyButton" Content="📖 Vocabulary" Classes="category-filter-button" 
                               Click="CategoryFilterButton_Click" Tag="VocabularyMastery"/>
                        <Button x:Name="QuizButton" Content="🧠 Quizzes" Classes="category-filter-button" 
                               Click="CategoryFilterButton_Click" Tag="QuizScore"/>
                        <Button x:Name="StreakButton" Content="🔥 Streaks" Classes="category-filter-button" 
                               Click="CategoryFilterButton_Click" Tag="StudyStreak"/>
                        <Button x:Name="TimeButton" Content="⏰ Time" Classes="category-filter-button" 
                               Click="CategoryFilterButton_Click" Tag="TimeSpent"/>
                    </StackPanel>
                </StackPanel>
            </Border>

            <!-- Achievements List -->
            <ItemsControl x:Name="AchievementsItemsControl">
                <ItemsControl.ItemTemplate>
                    <DataTemplate x:DataType="pages:AchievementViewModel">
                        <Border Classes="achievement-card" Margin="0,0,0,15">
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="Auto"/>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>

                                <!-- Achievement Icon -->
                                <Border Grid.Column="0" Width="70" Height="70" CornerRadius="35" 
                                       Background="{Binding BackgroundColor}" Margin="0,0,20,0"
                                       Opacity="{Binding IconOpacity}">
                                    <TextBlock Text="{Binding Icon}" FontSize="32" 
                                              HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                </Border>

                                <!-- Achievement Info -->
                                <StackPanel Grid.Column="1" VerticalAlignment="Center">
                                    <TextBlock Text="{Binding Name}" FontSize="18" FontWeight="Bold" 
                                              Foreground="#333"/>
                                    <TextBlock Text="{Binding Description}" FontSize="14" 
                                              Foreground="#666" TextWrapping="Wrap" Margin="0,4,0,8"/>
                                    
                                    <!-- Progress Bar -->
                                    <Grid Margin="0,0,0,5">
                                        <Border Background="#E0E0E0" Height="8" CornerRadius="4"/>
                                        <Border Background="{Binding ProgressColor}" Height="8" CornerRadius="4"
                                               Width="{Binding ProgressWidth}" HorizontalAlignment="Left"/>
                                    </Grid>
                                    
                                    <TextBlock Text="{Binding ProgressText}" FontSize="12" 
                                              Foreground="#888"/>
                                </StackPanel>

                                <!-- Achievement Status -->
                                <StackPanel Grid.Column="2" VerticalAlignment="Center" HorizontalAlignment="Center">
                                    <Border Background="{Binding StatusColor}" CornerRadius="15" Padding="12,6">
                                        <TextBlock Text="{Binding StatusText}" FontSize="12" 
                                                  Foreground="White" FontWeight="Medium"/>
                                    </Border>
                                    <TextBlock Text="{Binding PointsText}" FontSize="14" FontWeight="Bold" 
                                              Foreground="#667eea" HorizontalAlignment="Center" Margin="0,8,0,0"/>
                                </StackPanel>
                            </Grid>
                        </Border>
                    </DataTemplate>
                </ItemsControl.ItemTemplate>
            </ItemsControl>

            <!-- Empty State -->
            <Border x:Name="EmptyStatePanel" Classes="modern-card" IsVisible="False">
                <StackPanel HorizontalAlignment="Center" Spacing="15">
                    <TextBlock Text="🏆" FontSize="48" HorizontalAlignment="Center"/>
                    <TextBlock Text="No achievements found" FontSize="18" FontWeight="Bold" 
                              HorizontalAlignment="Center"/>
                    <TextBlock Text="Start learning to unlock your first achievement!" 
                              FontSize="14" Foreground="#666" HorizontalAlignment="Center"/>
                </StackPanel>
            </Border>
        </StackPanel>
    </ScrollViewer>
</UserControl>
