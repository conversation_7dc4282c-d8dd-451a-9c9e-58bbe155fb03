using System.Collections.ObjectModel;
using Avalonia.Controls;
using Avalonia.Interactivity;
using JavaOtomasyonApp.Data;
using JavaOtomasyonApp.Models;
using JavaOtomasyonApp.Services;
using Microsoft.EntityFrameworkCore;

namespace JavaOtomasyonApp.Views.Pages
{
    public partial class AchievementsPage : UserControl
    {
        public ObservableCollection<AchievementViewModel> Achievements { get; set; }
        private List<Achievement> _allAchievements = new();
        private List<UserAchievement> _userAchievements = new();

        public AchievementsPage()
        {
            InitializeComponent();
            Achievements = new ObservableCollection<AchievementViewModel>();
            AchievementsItemsControl.ItemsSource = Achievements;
            LoadAchievements();
        }

        private async void LoadAchievements()
        {
            try
            {
                if (AuthenticationService.CurrentUser == null) return;

                using var context = new AppDbContext();
                var userId = AuthenticationService.CurrentUser.Id;

                // Load all achievements
                _allAchievements = await context.Achievements
                    .Where(a => a.IsActive)
                    .OrderBy(a => a.Type)
                    .ThenBy(a => a.RequiredValue)
                    .ToListAsync();

                // Load user achievements
                _userAchievements = await context.UserAchievements
                    .Where(ua => ua.UserId == userId)
                    .Include(ua => ua.Achievement)
                    .ToListAsync();

                // Update summary
                var earnedCount = _userAchievements.Count(ua => ua.IsCompleted);
                var totalPoints = _userAchievements.Where(ua => ua.IsCompleted).Sum(ua => ua.Achievement.Points);
                var completionRate = _allAchievements.Any() ? (double)earnedCount / _allAchievements.Count * 100 : 0;

                EarnedAchievementsTextBlock.Text = earnedCount.ToString();
                TotalPointsTextBlock.Text = totalPoints.ToString();
                CompletionRateTextBlock.Text = $"{completionRate:F0}%";

                // Load achievements with progress
                await LoadAchievementProgress();
                ApplyFilter("All");
            }
            catch (Exception ex)
            {
                EmptyStatePanel.IsVisible = true;
            }
        }

        private async Task LoadAchievementProgress()
        {
            if (AuthenticationService.CurrentUser == null) return;

            using var context = new AppDbContext();
            var userId = AuthenticationService.CurrentUser.Id;

            foreach (var achievement in _allAchievements)
            {
                var userAchievement = _userAchievements.FirstOrDefault(ua => ua.AchievementId == achievement.Id);
                int currentValue = 0;

                // Calculate current progress based on achievement type
                switch (achievement.Type)
                {
                    case AchievementType.CourseCompletion:
                        currentValue = await context.UserProgresses
                            .CountAsync(up => up.UserId == userId && up.Status == ProgressStatus.Completed);
                        break;

                    case AchievementType.VocabularyMastery:
                        currentValue = await context.UserVocabularies
                            .CountAsync(uv => uv.UserId == userId && uv.MasteryLevel >= 4);
                        break;

                    case AchievementType.LessonCompletion:
                        currentValue = await context.LessonProgresses
                            .CountAsync(lp => lp.UserId == userId && lp.Status == ProgressStatus.Completed);
                        break;

                    case AchievementType.TimeSpent:
                        currentValue = await context.UserProgresses
                            .Where(up => up.UserId == userId)
                            .SumAsync(up => up.TimeSpentMinutes);
                        break;

                    case AchievementType.StudyStreak:
                        // TODO: Implement streak calculation
                        currentValue = 0;
                        break;

                    case AchievementType.QuizScore:
                        // TODO: Implement quiz score calculation
                        currentValue = 0;
                        break;
                }

                // Update or create user achievement
                if (userAchievement == null)
                {
                    userAchievement = new UserAchievement
                    {
                        UserId = userId,
                        AchievementId = achievement.Id,
                        Achievement = achievement,
                        CurrentValue = currentValue,
                        IsCompleted = currentValue >= achievement.RequiredValue
                    };
                    _userAchievements.Add(userAchievement);
                }
                else
                {
                    userAchievement.CurrentValue = currentValue;
                    userAchievement.IsCompleted = currentValue >= achievement.RequiredValue;
                }
            }
        }

        private void ApplyFilter(string filter)
        {
            Achievements.Clear();

            var filteredAchievements = _allAchievements.AsEnumerable();

            if (filter != "All")
            {
                if (Enum.TryParse<AchievementType>(filter, out var achievementType))
                {
                    filteredAchievements = filteredAchievements.Where(a => a.Type == achievementType);
                }
            }

            foreach (var achievement in filteredAchievements)
            {
                var userAchievement = _userAchievements.FirstOrDefault(ua => ua.AchievementId == achievement.Id);
                Achievements.Add(new AchievementViewModel(achievement, userAchievement));
            }

            EmptyStatePanel.IsVisible = !Achievements.Any();
        }

        private void CategoryFilterButton_Click(object? sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.Tag is string filter)
            {
                ApplyFilter(filter);
            }
        }
    }

    public class AchievementViewModel
    {
        public AchievementViewModel(Achievement achievement, UserAchievement? userAchievement = null)
        {
            Id = achievement.Id;
            Name = achievement.Name;
            Description = achievement.Description;
            Icon = achievement.Icon;
            Color = achievement.Color;
            RequiredValue = achievement.RequiredValue;
            Points = achievement.Points;
            Type = achievement.Type;

            CurrentValue = userAchievement?.CurrentValue ?? 0;
            IsCompleted = userAchievement?.IsCompleted ?? false;
            EarnedDate = userAchievement?.EarnedDate;
        }

        public int Id { get; set; }
        public string Name { get; set; }
        public string Description { get; set; }
        public string Icon { get; set; }
        public string Color { get; set; }
        public int RequiredValue { get; set; }
        public int Points { get; set; }
        public AchievementType Type { get; set; }
        public int CurrentValue { get; set; }
        public bool IsCompleted { get; set; }
        public DateTime? EarnedDate { get; set; }

        public string BackgroundColor => IsCompleted ? Color : "#F5F5F5";
        public double IconOpacity => IsCompleted ? 1.0 : 0.5;
        public string StatusText => IsCompleted ? "Completed" : "In Progress";
        public string StatusColor => IsCompleted ? "#4CAF50" : "#FF9800";
        public string ProgressColor => IsCompleted ? "#4CAF50" : "#667eea";
        public string PointsText => $"{Points} pts";
        public string ProgressText => $"{CurrentValue}/{RequiredValue}";
        public double ProgressPercentage => RequiredValue > 0 ? Math.Min(100, (double)CurrentValue / RequiredValue * 100) : 0;
        public double ProgressWidth => ProgressPercentage * 2; // Adjust based on container width
    }
}
