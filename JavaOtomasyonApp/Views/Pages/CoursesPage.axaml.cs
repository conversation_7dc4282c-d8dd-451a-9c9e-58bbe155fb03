using System.Collections.ObjectModel;
using Avalonia.Controls;
using Avalonia.Interactivity;
using JavaOtomasyonApp.Data;
using JavaOtomasyonApp.Models;
using Microsoft.EntityFrameworkCore;

namespace JavaOtomasyonApp.Views.Pages
{
    public partial class CoursesPage : UserControl
    {
        public ObservableCollection<CourseViewModel> Courses { get; set; }
        private List<Course> _allCourses = new();

        public CoursesPage()
        {
            InitializeComponent();
            Courses = new ObservableCollection<CourseViewModel>();
            CoursesItemsControl.ItemsSource = Courses;
            InitializeFilters();
            LoadCourses();
        }

        private void InitializeFilters()
        {
            // Level filter
            LevelFilterComboBox.Items.Add("All Levels");
            LevelFilterComboBox.Items.Add("Beginner");
            LevelFilterComboBox.Items.Add("Intermediate");
            LevelFilterComboBox.Items.Add("Advanced");
            LevelFilterComboBox.SelectedIndex = 0;

            // Type filter
            TypeFilterComboBox.Items.Add("All Types");
            TypeFilterComboBox.Items.Add("Grammar");
            TypeFilterComboBox.Items.Add("Vocabulary");
            TypeFilterComboBox.Items.Add("Speaking");
            TypeFilterComboBox.Items.Add("Listening");
            TypeFilterComboBox.Items.Add("Reading");
            TypeFilterComboBox.Items.Add("Writing");
            TypeFilterComboBox.Items.Add("Pronunciation");
            TypeFilterComboBox.SelectedIndex = 0;
        }

        private async void LoadCourses()
        {
            try
            {
                using var context = new AppDbContext();
                _allCourses = await context.Courses
                    .Where(c => c.IsActive)
                    .OrderBy(c => c.OrderIndex)
                    .ToListAsync();

                ApplyFilters();
            }
            catch (Exception ex)
            {
                // Handle error
                EmptyStatePanel.IsVisible = true;
            }
        }

        private void ApplyFilters()
        {
            Courses.Clear();

            var filteredCourses = _allCourses.AsEnumerable();

            // Level filter
            if (LevelFilterComboBox.SelectedIndex > 0)
            {
                var selectedLevel = (CourseLevel)(LevelFilterComboBox.SelectedIndex);
                filteredCourses = filteredCourses.Where(c => c.Level == selectedLevel);
            }

            // Type filter
            if (TypeFilterComboBox.SelectedIndex > 0)
            {
                var selectedType = (CourseType)(TypeFilterComboBox.SelectedIndex);
                filteredCourses = filteredCourses.Where(c => c.Type == selectedType);
            }

            foreach (var course in filteredCourses)
            {
                Courses.Add(new CourseViewModel(course));
            }

            EmptyStatePanel.IsVisible = !Courses.Any();
        }

        private void FilterButton_Click(object? sender, RoutedEventArgs e)
        {
            ApplyFilters();
        }

        private void ClearFilterButton_Click(object? sender, RoutedEventArgs e)
        {
            LevelFilterComboBox.SelectedIndex = 0;
            TypeFilterComboBox.SelectedIndex = 0;
            ApplyFilters();
        }

        private void ViewCourseButton_Click(object? sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.Tag is int courseId)
            {
                // TODO: Navigate to course details page
            }
        }

        private void EnrollCourseButton_Click(object? sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.Tag is int courseId)
            {
                // TODO: Navigate to payment page
            }
        }
    }

    public class CourseViewModel
    {
        public CourseViewModel(Course course)
        {
            Id = course.Id;
            Title = course.Title;
            Description = course.Description;
            Level = course.Level;
            Type = course.Type;
            Price = course.Price;
            EstimatedDurationMinutes = course.EstimatedDurationMinutes;
            Color = course.Color ?? "#667eea";
            LevelDisplayName = course.LevelDisplayName;
            TypeDisplayName = course.TypeDisplayName;
            FormattedPrice = course.FormattedPrice;
            EstimatedDuration = course.EstimatedDuration;
        }

        public int Id { get; set; }
        public string Title { get; set; }
        public string Description { get; set; }
        public CourseLevel Level { get; set; }
        public CourseType Type { get; set; }
        public decimal Price { get; set; }
        public int EstimatedDurationMinutes { get; set; }
        public string Color { get; set; }
        public string LevelDisplayName { get; set; }
        public string TypeDisplayName { get; set; }
        public string FormattedPrice { get; set; }
        public string EstimatedDuration { get; set; }

        public string TypeIcon => Type switch
        {
            CourseType.Grammar => "📝",
            CourseType.Vocabulary => "📖",
            CourseType.Speaking => "🗣️",
            CourseType.Listening => "👂",
            CourseType.Reading => "📚",
            CourseType.Writing => "✍️",
            CourseType.Pronunciation => "🔊",
            _ => "📚"
        };
    }
}
