<UserControl xmlns="https://github.com/avaloniaui"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:pages="using:JavaOtomasyonApp.Views.Pages"
             x:Class="JavaOtomasyonApp.Views.Pages.DashboardPage">

    <ScrollViewer VerticalScrollBarVisibility="Auto">
        <StackPanel Margin="20">
            <!-- Welcome Section -->
            <Border CornerRadius="20" Padding="40" Margin="0,0,0,30">
                <Border.Background>
                    <LinearGradientBrush StartPoint="0%,0%" EndPoint="100%,100%">
                        <GradientStop Color="#667eea" Offset="0"/>
                        <GradientStop Color="#764ba2" Offset="1"/>
                    </LinearGradientBrush>
                </Border.Background>
                <Border.Effect>
                    <DropShadowEffect Color="Black" Opacity="0.2" BlurRadius="20" OffsetY="10"/>
                </Border.Effect>

                <StackPanel>
                    <TextBlock x:Name="WelcomeTextBlock"
                              Text="Welcome to English Learning! 🌟"
                              FontSize="32" FontWeight="Bold"
                              Foreground="White" Margin="0,0,0,15"/>
                    <TextBlock x:Name="MotivationTextBlock"
                              Text="Continue your English learning journey and make progress today!"
                              FontSize="18" Foreground="White" TextWrapping="Wrap" Opacity="0.9"/>
                </StackPanel>
            </Border>

            <!-- Statistics Cards -->
            <Grid Margin="0,0,0,30">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <!-- Total Courses Card -->
                <Border Grid.Column="0" Classes="modern-card" Margin="0,0,10,0">
                    <StackPanel HorizontalAlignment="Center">
                        <TextBlock Text="📚" FontSize="36" HorizontalAlignment="Center" Margin="0,0,0,15"/>
                        <TextBlock x:Name="TotalCoursesTextBlock" Text="0"
                                  FontSize="28" FontWeight="Bold"
                                  HorizontalAlignment="Center" Margin="0,0,0,8"
                                  Foreground="#667eea"/>
                        <TextBlock Text="Total Courses" FontSize="13" FontWeight="Medium"
                                  Foreground="#666666" HorizontalAlignment="Center"/>
                    </StackPanel>
                </Border>

                <!-- Completed Courses Card -->
                <Border Grid.Column="1" Classes="modern-card" Margin="5,0">
                    <StackPanel HorizontalAlignment="Center">
                        <TextBlock Text="✅" FontSize="36" HorizontalAlignment="Center" Margin="0,0,0,15"/>
                        <TextBlock x:Name="CompletedCoursesTextBlock" Text="0"
                                  FontSize="28" FontWeight="Bold"
                                  HorizontalAlignment="Center" Margin="0,0,0,8"
                                  Foreground="#4CAF50"/>
                        <TextBlock Text="Completed" FontSize="13" FontWeight="Medium"
                                  Foreground="#666666" HorizontalAlignment="Center"/>
                    </StackPanel>
                </Border>

                <!-- Vocabulary Words Card -->
                <Border Grid.Column="2" Classes="modern-card" Margin="5,0">
                    <StackPanel HorizontalAlignment="Center">
                        <TextBlock Text="📖" FontSize="36" HorizontalAlignment="Center" Margin="0,0,0,15"/>
                        <TextBlock x:Name="VocabularyWordsTextBlock" Text="0"
                                  FontSize="28" FontWeight="Bold"
                                  HorizontalAlignment="Center" Margin="0,0,0,8"
                                  Foreground="#FF9800"/>
                        <TextBlock Text="Words Learned" FontSize="13" FontWeight="Medium"
                                  Foreground="#666666" HorizontalAlignment="Center"/>
                    </StackPanel>
                </Border>

                <!-- Total Time Card -->
                <Border Grid.Column="3" Classes="modern-card" Margin="10,0,0,0">
                    <StackPanel HorizontalAlignment="Center">
                        <TextBlock Text="⏰" FontSize="36" HorizontalAlignment="Center" Margin="0,0,0,15"/>
                        <TextBlock x:Name="TotalTimeTextBlock" Text="0h"
                                  FontSize="28" FontWeight="Bold"
                                  HorizontalAlignment="Center" Margin="0,0,0,8"
                                  Foreground="#9C27B0"/>
                        <TextBlock Text="Study Time" FontSize="13" FontWeight="Medium"
                                  Foreground="#666666" HorizontalAlignment="Center"/>
                    </StackPanel>
                </Border>
            </Grid>

            <!-- Quick Actions -->
            <Border Classes="modern-card" Margin="0,0,0,30">
                <StackPanel>
                    <TextBlock Text="Quick Actions 🚀" Classes="modern-subtitle" Margin="0,0,0,20"/>

                    <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" Spacing="15">
                        <Button x:Name="ViewCoursesButton" Content="📚 View Courses"
                               Classes="action-button-primary"
                               Click="ViewCoursesButton_Click"/>

                        <Button x:Name="ContinueLearningButton" Content="🎯 Continue Learning"
                               Classes="action-button-success"
                               Click="ContinueLearningButton_Click"/>

                        <Button x:Name="ViewProgressButton" Content="📈 View Progress"
                               Classes="action-button-info"
                               Click="ViewProgressButton_Click"/>
                    </StackPanel>
                </StackPanel>
            </Border>

            <!-- Recent Activity -->
            <Border Classes="modern-card">
                <StackPanel>
                    <TextBlock Text="Recent Activities 📋" Classes="modern-subtitle" Margin="0,0,0,20"/>

                    <ListBox x:Name="RecentActivityListBox"
                             BorderThickness="0" Background="Transparent">
                        <ListBox.ItemTemplate>
                            <DataTemplate x:DataType="pages:ActivityItem">
                                <Border Classes="activity-card" Margin="0,8">
                                    <Grid>
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="Auto"/>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="Auto"/>
                                        </Grid.ColumnDefinitions>

                                        <Border Grid.Column="0" Background="#F0F8FF"
                                               CornerRadius="25" Width="50" Height="50"
                                               VerticalAlignment="Center" Margin="0,0,15,0">
                                            <TextBlock Text="{Binding Icon}"
                                                      FontSize="24" HorizontalAlignment="Center"
                                                      VerticalAlignment="Center"/>
                                        </Border>

                                        <StackPanel Grid.Column="1" VerticalAlignment="Center">
                                            <TextBlock Text="{Binding Title}" FontWeight="SemiBold" FontSize="14"/>
                                            <TextBlock Text="{Binding Description}"
                                                      FontSize="12" Foreground="#666666" Margin="0,2,0,0"/>
                                        </StackPanel>

                                        <TextBlock Grid.Column="2" Text="{Binding Time}"
                                                  FontSize="11" Foreground="#888"
                                                  VerticalAlignment="Center"/>
                                    </Grid>
                                </Border>
                            </DataTemplate>
                        </ListBox.ItemTemplate>
                    </ListBox>
                </StackPanel>
            </Border>
        </StackPanel>
    </ScrollViewer>
</UserControl>
