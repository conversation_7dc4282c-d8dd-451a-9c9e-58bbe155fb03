<UserControl x:Class="JavaOtomasyonApp.Views.Pages.DashboardPage"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">

    <ScrollViewer VerticalScrollBarVisibility="Auto">
        <StackPanel Margin="20">
            <!-- Welcome Section -->
            <Border CornerRadius="20" Padding="40" Margin="0,0,0,30">
                <Border.Background>
                    <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                        <GradientStop Color="#667eea" Offset="0"/>
                        <GradientStop Color="#764ba2" Offset="1"/>
                    </LinearGradientBrush>
                </Border.Background>
                <Border.Effect>
                    <DropShadowEffect Color="Black" Opacity="0.2" BlurRadius="20" ShadowDepth="10"/>
                </Border.Effect>

                <StackPanel>
                    <TextBlock x:Name="WelcomeTextBlock"
                              Text="Welcome to English Automation! 🌟"
                              FontSize="32" FontWeight="Bold"
                              Foreground="White" Margin="0,0,0,15"/>
                    <TextBlock x:Name="MotivationTextBlock"
                              Text="Continue your English automation learning journey and make progress today!"
                              FontSize="18" Foreground="White" TextWrapping="Wrap" Opacity="0.9"/>
                </StackPanel>
            </Border>

            <!-- Statistics Cards -->
            <Grid Margin="0,0,0,30">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <!-- Total Courses Card -->
                <Border Grid.Column="0" Style="{StaticResource CardBorderStyle}" Margin="0,0,10,0">
                    <StackPanel HorizontalAlignment="Center">
                        <TextBlock Text="📚" FontSize="36" HorizontalAlignment="Center" Margin="0,0,0,10"/>
                        <TextBlock x:Name="TotalCoursesTextBlock" Text="0" FontSize="28" FontWeight="Bold"
                                  HorizontalAlignment="Center" Foreground="#667eea"/>
                        <TextBlock Text="Total Courses" FontSize="14" HorizontalAlignment="Center" Foreground="#666"/>
                    </StackPanel>
                </Border>

                <!-- Completed Courses Card -->
                <Border Grid.Column="1" Style="{StaticResource CardBorderStyle}" Margin="5,0">
                    <StackPanel HorizontalAlignment="Center">
                        <TextBlock Text="✅" FontSize="36" HorizontalAlignment="Center" Margin="0,0,0,10"/>
                        <TextBlock x:Name="CompletedCoursesTextBlock" Text="0" FontSize="28" FontWeight="Bold"
                                  HorizontalAlignment="Center" Foreground="#4CAF50"/>
                        <TextBlock Text="Completed" FontSize="14" HorizontalAlignment="Center" Foreground="#666"/>
                    </StackPanel>
                </Border>

                <!-- Study Time Card -->
                <Border Grid.Column="2" Style="{StaticResource CardBorderStyle}" Margin="5,0">
                    <StackPanel HorizontalAlignment="Center">
                        <TextBlock Text="⏱️" FontSize="36" HorizontalAlignment="Center" Margin="0,0,0,10"/>
                        <TextBlock x:Name="StudyTimeTextBlock" Text="0h" FontSize="28" FontWeight="Bold"
                                  HorizontalAlignment="Center" Foreground="#FF9800"/>
                        <TextBlock Text="Study Time" FontSize="14" HorizontalAlignment="Center" Foreground="#666"/>
                    </StackPanel>
                </Border>

                <!-- Vocabulary Card -->
                <Border Grid.Column="3" Style="{StaticResource CardBorderStyle}" Margin="10,0,0,0">
                    <StackPanel HorizontalAlignment="Center">
                        <TextBlock Text="📖" FontSize="36" HorizontalAlignment="Center" Margin="0,0,0,10"/>
                        <TextBlock x:Name="VocabularyCountTextBlock" Text="0" FontSize="28" FontWeight="Bold"
                                  HorizontalAlignment="Center" Foreground="#9C27B0"/>
                        <TextBlock Text="Words Learned" FontSize="14" HorizontalAlignment="Center" Foreground="#666"/>
                    </StackPanel>
                </Border>
            </Grid>

            <!-- Quick Actions -->
            <Border Style="{StaticResource CardBorderStyle}">
                <StackPanel>
                    <TextBlock Text="Quick Actions" Style="{StaticResource SubtitleTextStyle}"/>
                    
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>

                        <Button Grid.Column="0" Content="📚 Browse Courses"
                               Style="{StaticResource PrimaryActionButtonStyle}"
                               Click="BrowseCoursesButton_Click" Margin="0,0,10,0"/>

                        <Button Grid.Column="1" Content="📖 Study Vocabulary"
                               Style="{StaticResource PrimaryActionButtonStyle}"
                               Click="StudyVocabularyButton_Click" Margin="5,0"/>

                        <Button Grid.Column="2" Content="📊 View Progress"
                               Style="{StaticResource PrimaryActionButtonStyle}"
                               Click="ViewProgressButton_Click" Margin="10,0,0,0"/>
                    </Grid>
                </StackPanel>
            </Border>

            <!-- Recent Activity -->
            <Border Style="{StaticResource CardBorderStyle}">
                <StackPanel>
                    <TextBlock Text="Recent Activity" Style="{StaticResource SubtitleTextStyle}"/>
                    
                    <StackPanel x:Name="RecentActivityPanel">
                        <TextBlock Text="No recent activity" FontStyle="Italic" Foreground="#999" 
                                  HorizontalAlignment="Center" Margin="0,20"/>
                    </StackPanel>
                </StackPanel>
            </Border>
        </StackPanel>
    </ScrollViewer>
</UserControl>
