using System;
using System.Collections.ObjectModel;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using JavaOtomasyonApp.Data;
using JavaOtomasyonApp.Models;
using JavaOtomasyonApp.Services;
using Microsoft.EntityFrameworkCore;

namespace JavaOtomasyonApp.Views.Pages
{
    public partial class DashboardPage : UserControl
    {
        public ObservableCollection<ActivityItem> RecentActivities { get; set; }

        public DashboardPage()
        {
            InitializeComponent();
            RecentActivities = new ObservableCollection<ActivityItem>();
            LoadDashboardData();
        }

        private async void LoadDashboardData()
        {
            try
            {
                if (AuthenticationService.CurrentUser == null) return;

                using var context = new AppDbContext();
                var userId = AuthenticationService.CurrentUser.Id;

                // İstatistikleri yükle
                var totalCourses = await context.Courses.CountAsync(c => c.IsActive);
                var userProgresses = await context.UserProgresses
                    .Where(up => up.UserId == userId)
                    .Include(up => up.Course)
                    .ToListAsync();

                var completedCourses = userProgresses.Count(up => up.Status == ProgressStatus.Completed);
                var totalTimeMinutes = userProgresses.Sum(up => up.TimeSpentMinutes);

                // Kelime sayısını hesapla
                var vocabularyCount = await context.UserVocabularies
                    .Where(uv => uv.UserId == userId && uv.MasteryLevel >= 3)
                    .CountAsync();

                // UI'yi güncelle
                TotalCoursesTextBlock.Text = totalCourses.ToString();
                CompletedCoursesTextBlock.Text = completedCourses.ToString();
                VocabularyWordsTextBlock.Text = vocabularyCount.ToString();
                TotalTimeTextBlock.Text = FormatTime(totalTimeMinutes);

                // Hoş geldin mesajını güncelle
                WelcomeTextBlock.Text = $"Welcome back, {AuthenticationService.CurrentUser.FirstName}! 🌟";

                // Son aktiviteleri yükle
                await LoadRecentActivities();
            }
            catch (Exception ex)
            {
                // Hata durumunda basit mesaj
                WelcomeTextBlock.Text = $"Error loading data: {ex.Message}";
            }
        }

        private async Task LoadRecentActivities()
        {
            try
            {
                RecentActivities.Clear();

                using var context = new AppDbContext();
                var userId = AuthenticationService.CurrentUser!.Id;

                // Son ödemeler
                var recentPayments = await context.Payments
                    .Where(p => p.UserId == userId && p.Status == PaymentStatus.Completed)
                    .Include(p => p.Course)
                    .OrderByDescending(p => p.CompletedDate)
                    .Take(3)
                    .ToListAsync();

                foreach (var payment in recentPayments)
                {
                    RecentActivities.Add(new ActivityItem
                    {
                        Icon = "💳",
                        Title = $"{payment.Course.Title} - Payment Completed",
                        Description = $"{payment.FormattedAmount} payment made",
                        Time = GetRelativeTime(payment.CompletedDate ?? payment.PaymentDate)
                    });
                }

                // Son tamamlanan dersler
                var recentCompletions = await context.UserProgresses
                    .Where(up => up.UserId == userId && up.Status == ProgressStatus.Completed)
                    .Include(up => up.Course)
                    .OrderByDescending(up => up.CompletedDate)
                    .Take(2)
                    .ToListAsync();

                foreach (var completion in recentCompletions)
                {
                    RecentActivities.Add(new ActivityItem
                    {
                        Icon = "🎓",
                        Title = $"{completion.Course.Title} - Course Completed",
                        Description = $"Congratulations! You completed this course",
                        Time = GetRelativeTime(completion.CompletedDate ?? DateTime.Now)
                    });
                }

                // Eğer aktivite yoksa varsayılan mesaj ekle
                if (!RecentActivities.Any())
                {
                    RecentActivities.Add(new ActivityItem
                    {
                        Icon = "🚀",
                        Title = "Start your learning journey!",
                        Description = "Begin by purchasing your first course",
                        Time = "Now"
                    });
                }
            }
            catch (Exception ex)
            {
                RecentActivities.Add(new ActivityItem
                {
                    Icon = "❌",
                    Title = "Error",
                    Description = $"Could not load activities: {ex.Message}",
                    Time = "Now"
                });
            }
        }

        private string FormatTime(int minutes)
        {
            if (minutes < 60)
                return $"{minutes}m";
            
            var hours = minutes / 60;
            var remainingMinutes = minutes % 60;
            
            if (remainingMinutes == 0)
                return $"{hours}h";
            
            return $"{hours}h {remainingMinutes}m";
        }

        private string GetRelativeTime(DateTime dateTime)
        {
            var timeSpan = DateTime.Now - dateTime;

            if (timeSpan.TotalMinutes < 1)
                return "Just now";
            if (timeSpan.TotalMinutes < 60)
                return $"{(int)timeSpan.TotalMinutes} minutes ago";
            if (timeSpan.TotalHours < 24)
                return $"{(int)timeSpan.TotalHours} hours ago";
            if (timeSpan.TotalDays < 7)
                return $"{(int)timeSpan.TotalDays} days ago";

            return dateTime.ToString("MM/dd/yyyy");
        }

        private void BrowseCoursesButton_Click(object sender, RoutedEventArgs e)
        {
            // Navigate to courses page
            var mainWindow = Window.GetWindow(this) as MainWindow;
            if (mainWindow != null)
            {
                mainWindow.NavigateToCoursesPage();
            }
        }

        private void StudyVocabularyButton_Click(object sender, RoutedEventArgs e)
        {
            // Navigate to vocabulary page
            var mainWindow = Window.GetWindow(this) as MainWindow;
            if (mainWindow != null)
            {
                mainWindow.NavigateToPage(new VocabularyPage());
            }
        }

        private void ViewProgressButton_Click(object sender, RoutedEventArgs e)
        {
            // Navigate to progress page
            var mainWindow = Window.GetWindow(this) as MainWindow;
            if (mainWindow != null)
            {
                mainWindow.NavigateToProgressPage();
            }
        }
    }

    public class ActivityItem
    {
        public string Icon { get; set; } = string.Empty;
        public string Title { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string Time { get; set; } = string.Empty;
    }
}
