<UserControl xmlns="https://github.com/avaloniaui"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:pages="using:JavaOtomasyonApp.Views.Pages"
             x:Class="JavaOtomasyonApp.Views.Pages.CoursesPage">

    <ScrollViewer VerticalScrollBarVisibility="Auto">
        <StackPanel Margin="20">
            <TextBlock Text="📚 English Courses" Classes="title"/>
            <TextBlock Text="Choose courses that match your level and start your English learning journey!"
                      FontSize="16" Foreground="#666666" TextWrapping="Wrap" Margin="0,0,0,30"/>

            <!-- Course Filter -->
            <Border Classes="modern-card" Margin="0,0,0,20">
                <StackPanel>
                    <TextBlock Text="Filter Courses 🔍" Classes="modern-subtitle" Margin="0,0,0,15"/>
                    <StackPanel Orientation="Horizontal" Spacing="15">
                        <ComboBox x:Name="LevelFilterComboBox" PlaceholderText="Select Level" Width="150"/>
                        <ComboBox x:Name="TypeFilterComboBox" PlaceholderText="Select Type" Width="150"/>
                        <Button x:Name="FilterButton" Content="Apply Filter" Classes="action-button-primary" Click="FilterButton_Click"/>
                        <Button x:Name="ClearFilterButton" Content="Clear" Classes="action-button-info" Click="ClearFilterButton_Click"/>
                    </StackPanel>
                </StackPanel>
            </Border>

            <!-- Courses List -->
            <ItemsControl x:Name="CoursesItemsControl">
                <ItemsControl.ItemTemplate>
                    <DataTemplate x:DataType="pages:CourseViewModel">
                        <Border Classes="course-card" Margin="0,0,0,20">
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="Auto"/>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>

                                <!-- Course Icon -->
                                <Border Grid.Column="0" Width="80" Height="80" CornerRadius="15"
                                       Background="{Binding Color}" Margin="0,0,20,0">
                                    <TextBlock Text="{Binding TypeIcon}" FontSize="32"
                                              HorizontalAlignment="Center" VerticalAlignment="Center"
                                              Foreground="White"/>
                                </Border>

                                <!-- Course Info -->
                                <StackPanel Grid.Column="1" VerticalAlignment="Center">
                                    <TextBlock Text="{Binding Title}" FontSize="18" FontWeight="Bold"
                                              Foreground="#333" Margin="0,0,0,5"/>
                                    <TextBlock Text="{Binding Description}" FontSize="14"
                                              Foreground="#666" TextWrapping="Wrap" Margin="0,0,0,8"/>
                                    <StackPanel Orientation="Horizontal" Spacing="15">
                                        <Border Background="#E3F2FD" CornerRadius="12" Padding="8,4">
                                            <TextBlock Text="{Binding LevelDisplayName}" FontSize="12"
                                                      Foreground="#1976D2" FontWeight="Medium"/>
                                        </Border>
                                        <Border Background="#F3E5F5" CornerRadius="12" Padding="8,4">
                                            <TextBlock Text="{Binding TypeDisplayName}" FontSize="12"
                                                      Foreground="#7B1FA2" FontWeight="Medium"/>
                                        </Border>
                                        <TextBlock Text="{Binding EstimatedDuration}" FontSize="12"
                                                  Foreground="#888" VerticalAlignment="Center"/>
                                    </StackPanel>
                                </StackPanel>

                                <!-- Course Actions -->
                                <StackPanel Grid.Column="2" VerticalAlignment="Center" Spacing="10">
                                    <TextBlock Text="{Binding FormattedPrice}" FontSize="20" FontWeight="Bold"
                                              Foreground="#4CAF50" HorizontalAlignment="Center"/>
                                    <Button Content="📖 View Details" Classes="action-button-primary"
                                           Tag="{Binding Id}" Click="ViewCourseButton_Click"/>
                                    <Button Content="🛒 Enroll" Classes="action-button-success"
                                           Tag="{Binding Id}" Click="EnrollCourseButton_Click"/>
                                </StackPanel>
                            </Grid>
                        </Border>
                    </DataTemplate>
                </ItemsControl.ItemTemplate>
            </ItemsControl>

            <!-- Loading/Empty State -->
            <Border x:Name="EmptyStatePanel" Classes="modern-card" IsVisible="False">
                <StackPanel HorizontalAlignment="Center" Spacing="15">
                    <TextBlock Text="📚" FontSize="48" HorizontalAlignment="Center"/>
                    <TextBlock Text="No courses found" FontSize="18" FontWeight="Bold"
                              HorizontalAlignment="Center"/>
                    <TextBlock Text="Try adjusting your filters or check back later for new courses."
                              FontSize="14" Foreground="#666" HorizontalAlignment="Center"/>
                </StackPanel>
            </Border>
        </StackPanel>
    </ScrollViewer>
</UserControl>
