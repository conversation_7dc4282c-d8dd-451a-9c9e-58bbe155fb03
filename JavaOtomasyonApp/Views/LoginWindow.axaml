<Window xmlns="https://github.com/avaloniaui"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        x:Class="JavaOtomasyonApp.Views.LoginWindow"
        Title="English Learning Platform - Login"
        Height="600" Width="450"
        WindowStartupLocation="CenterScreen"
        CanResize="False">

    <Window.Background>
        <LinearGradientBrush StartPoint="0%,0%" EndPoint="100%,100%">
            <GradientStop Color="#667eea" Offset="0"/>
            <GradientStop Color="#764ba2" Offset="1"/>
        </LinearGradientBrush>
    </Window.Background>
    
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" Padding="30,40,30,20">
            <StackPanel HorizontalAlignment="Center">
                <TextBlock Text="🌟" FontSize="64" HorizontalAlignment="Center" Foreground="White"/>
                <TextBlock Text="English Learning Platform"
                          FontSize="24" FontWeight="Bold"
                          HorizontalAlignment="Center" Foreground="White" Margin="0,10,0,0"/>
                <TextBlock Text="Master English with confidence"
                          FontSize="14"
                          HorizontalAlignment="Center" Foreground="White" Opacity="0.9" Margin="0,5,0,0"/>
            </StackPanel>
        </Border>

        <!-- Login Form -->
        <Border Grid.Row="1" CornerRadius="25" Margin="30,0,30,20" VerticalAlignment="Center">
            <Border.Background>
                <SolidColorBrush Color="White" Opacity="0.95"/>
            </Border.Background>
            <Border.Effect>
                <DropShadowEffect Color="Black" Opacity="0.2" BlurRadius="20" OffsetY="10"/>
            </Border.Effect>

            <StackPanel Margin="40,35" VerticalAlignment="Center">
                <TextBlock Text="Welcome Back! 👋" FontSize="28" FontWeight="Bold"
                          HorizontalAlignment="Center" Foreground="#333" Margin="0,0,0,30"/>

                <TextBlock Text="Email:" FontWeight="SemiBold" Margin="0,0,0,8" Foreground="#555"/>
                <TextBox x:Name="EmailTextBox" Classes="modern-login"
                        Margin="0,0,0,20" Watermark="Enter your email address"/>

                <TextBlock Text="Password:" FontWeight="SemiBold" Margin="0,0,0,8" Foreground="#555"/>
                <TextBox x:Name="PasswordTextBox" Classes="modern-login"
                        PasswordChar="*" Margin="0,0,0,25"
                        Watermark="Enter your password"/>

                <Button x:Name="LoginButton" Content="🚀 Sign In"
                       Classes="login-button" Click="LoginButton_Click"
                       Margin="0,0,0,15"/>

                <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,15">
                    <TextBlock Text="Don't have an account? " VerticalAlignment="Center" Foreground="#666"/>
                    <Button x:Name="RegisterButton" Content="Sign Up"
                           Background="Transparent" BorderThickness="0"
                           Foreground="#667eea" FontWeight="SemiBold"
                           Cursor="Hand" Click="RegisterButton_Click"/>
                </StackPanel>

                <Button x:Name="ForgotPasswordButton" Content="Forgot Password?"
                       Background="Transparent" BorderThickness="0"
                       Foreground="#888" FontSize="13"
                       HorizontalAlignment="Center" Cursor="Hand"
                       Click="ForgotPasswordButton_Click" Margin="0,5"/>

                <TextBlock x:Name="MessageTextBlock"
                          TextWrapping="Wrap" HorizontalAlignment="Center"
                          Margin="0,20,0,0" FontSize="13"/>
            </StackPanel>
        </Border>

        <!-- Footer -->
        <Border Grid.Row="2" Padding="15">
            <TextBlock Text="© 2024 English Learning Platform"
                      HorizontalAlignment="Center" FontSize="11"
                      Foreground="White" Opacity="0.8"/>
        </Border>
    </Grid>
</Window>
