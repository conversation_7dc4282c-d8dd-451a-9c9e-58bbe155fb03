<Window x:Class="JavaOtomasyonApp.Views.RegisterWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="Register - English Automation Platform"
        Height="600" Width="450"
        WindowStartupLocation="CenterOwner"
        ResizeMode="NoResize"
        Background="#F5F5F5">
    
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" Background="#007ACC" Padding="20">
            <StackPanel HorizontalAlignment="Center">
                <TextBlock Text="🚀" FontSize="48" HorizontalAlignment="Center" Foreground="White"/>
                <TextBlock Text="Create New Account" 
                          FontSize="18" FontWeight="Bold" 
                          HorizontalAlignment="Center" Foreground="White"/>
            </StackPanel>
        </Border>

        <!-- Register Form -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
            <Border Style="{StaticResource CardBorderStyle}" Margin="20" VerticalAlignment="Center">
                <StackPanel Margin="30" VerticalAlignment="Center">
                    <TextBlock Text="Join our English automation learning platform!" 
                              FontSize="14" HorizontalAlignment="Center" 
                              Foreground="#666" Margin="0,0,0,25"/>

                    <TextBlock Text="First Name:" FontWeight="SemiBold" Margin="0,0,0,5" Foreground="#555"/>
                    <TextBox x:Name="FirstNameTextBox" Style="{StaticResource ModernTextBoxStyle}"
                            Margin="0,0,0,15"/>

                    <TextBlock Text="Last Name:" FontWeight="SemiBold" Margin="0,0,0,5" Foreground="#555"/>
                    <TextBox x:Name="LastNameTextBox" Style="{StaticResource ModernTextBoxStyle}"
                            Margin="0,0,0,15"/>

                    <TextBlock Text="Email:" FontWeight="SemiBold" Margin="0,0,0,5" Foreground="#555"/>
                    <TextBox x:Name="EmailTextBox" Style="{StaticResource ModernTextBoxStyle}"
                            Margin="0,0,0,15"/>

                    <TextBlock Text="Password:" FontWeight="SemiBold" Margin="0,0,0,5" Foreground="#555"/>
                    <PasswordBox x:Name="PasswordBox" Style="{StaticResource LoginPasswordBoxStyle}"
                                Margin="0,0,0,15"/>

                    <TextBlock Text="Confirm Password:" FontWeight="SemiBold" Margin="0,0,0,5" Foreground="#555"/>
                    <PasswordBox x:Name="ConfirmPasswordBox" Style="{StaticResource LoginPasswordBoxStyle}"
                                Margin="0,0,0,20"/>

                    <Button x:Name="RegisterButton" Content="🎯 Create Account"
                           Style="{StaticResource LoginButtonStyle}" Click="RegisterButton_Click"
                           Margin="0,0,0,15"/>

                    <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,10">
                        <TextBlock Text="Already have an account? " VerticalAlignment="Center" Foreground="#666"/>
                        <Button x:Name="LoginButton" Content="Sign In"
                               Background="Transparent" BorderThickness="0"
                               Foreground="#007ACC" FontWeight="SemiBold"
                               Cursor="Hand" Click="LoginButton_Click"/>
                    </StackPanel>

                    <TextBlock x:Name="MessageTextBlock"
                              TextWrapping="Wrap" HorizontalAlignment="Center"
                              Margin="0,15,0,0" FontSize="13"/>
                </StackPanel>
            </Border>
        </ScrollViewer>

        <!-- Footer -->
        <Border Grid.Row="2" Padding="15">
            <TextBlock Text="© 2024 English Automation Platform"
                      HorizontalAlignment="Center" FontSize="11"
                      Foreground="#888"/>
        </Border>
    </Grid>
</Window>
