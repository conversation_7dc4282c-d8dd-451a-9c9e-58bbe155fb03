using System;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using JavaOtomasyonApp.Services;

namespace JavaOtomasyonApp.Views
{
    public partial class LoginWindow : Window
    {
        public LoginWindow()
        {
            InitializeComponent();
        }

        private async void LoginButton_Click(object sender, RoutedEventArgs e)
        {
            if (string.IsNullOrWhiteSpace(EmailTextBox.Text) ||
                string.IsNullOrWhiteSpace(PasswordBox.Password))
            {
                ShowMessage("Please fill in email and password fields.", false);
                return;
            }

            LoginButton.IsEnabled = false;
            LoginButton.Content = "Logging in...";

            try
            {
                var result = await AuthenticationService.LoginAsync(
                    EmailTextBox.Text.Trim(),
                    PasswordBox.Password);

                if (result.Success)
                {
                    ShowMessage(result.Message, true);
                    
                    // Ana pencereyi aç
                    var mainWindow = new MainWindow();
                    mainWindow.Show();
                    this.Close();
                }
                else
                {
                    ShowMessage(result.Message, false);
                }
            }
            catch (Exception ex)
            {
                ShowMessage($"Beklenmeyen hata: {ex.Message}", false);
            }
            finally
            {
                LoginButton.IsEnabled = true;
                LoginButton.Content = "Sign In";
            }
        }

        private void RegisterButton_Click(object sender, RoutedEventArgs e)
        {
            var registerWindow = new RegisterWindow();
            registerWindow.ShowDialog();
        }

        private async void ForgotPasswordButton_Click(object sender, RoutedEventArgs e)
        {
            if (string.IsNullOrWhiteSpace(EmailTextBox.Text))
            {
                ShowMessage("Please enter your email address.", false);
                return;
            }

            ForgotPasswordButton.IsEnabled = false;
            
            try
            {
                var result = await AuthenticationService.ResetPasswordAsync(EmailTextBox.Text.Trim());
                ShowMessage(result.Message, result.Success);
            }
            catch (Exception ex)
            {
                ShowMessage($"Error: {ex.Message}", false);
            }
            finally
            {
                ForgotPasswordButton.IsEnabled = true;
            }
        }

        private void ShowMessage(string message, bool isSuccess)
        {
            MessageTextBlock.Text = message;
            MessageTextBlock.Foreground = isSuccess ? 
                new SolidColorBrush(Colors.Green) : 
                new SolidColorBrush(Colors.Red);
        }
    }
}
