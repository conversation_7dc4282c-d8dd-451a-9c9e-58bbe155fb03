<Window xmlns="https://github.com/avaloniaui"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        x:Class="JavaOtomasyonApp.Views.ProfileWindow"
        Title="Profil Ayarları"
        Height="400" Width="500"
        WindowStartupLocation="CenterOwner"
        CanResize="False"
        Background="#F8F9FA">
    
    <StackPanel Margin="30">
        <TextBlock Text="👤 Profil" Classes="title"/>
        
        <Border Classes="card">
            <StackPanel>
                <TextBlock Text="Profil ayarları yükleniyor..." FontSize="16" HorizontalAlignment="Center"/>
                <TextBlock Text="Bu sayfa yakında tamamlanacak." FontSize="12" 
                          Foreground="#666666" HorizontalAlignment="Center" Margin="0,10,0,0"/>
                
                <Button Content="Kapat" Classes="modern" HorizontalAlignment="Center" 
                       Margin="0,20,0,0" Click="CloseButton_Click"/>
            </StackPanel>
        </Border>
    </StackPanel>
</Window>
