using System.Windows;
using System.Windows.Controls;
using JavaOtomasyonApp.Services;
using JavaOtomasyonApp.Views.Pages;

namespace JavaOtomasyonApp.Views
{
    public partial class MainWindow : Window
    {
        public MainWindow()
        {
            InitializeComponent();
            InitializeWindow();
        }

        private void InitializeWindow()
        {
            // Show user information
            if (AuthenticationService.CurrentUser != null)
            {
                UpdateWelcomeMessage();

                // Admin panel only visible for admin users
                if (AuthenticationService.IsAdmin)
                {
                    AdminButton.Visibility = Visibility.Visible;
                }
            }

            // Show Dashboard page by default
            NavigateToPage(new DashboardPage());
            UpdateStatus("Dashboard");
        }

        private void UpdateWelcomeMessage()
        {
            if (AuthenticationService.CurrentUser != null)
            {
                WelcomeTextBlock.Text = $"{LanguageService.GetText("WelcomeBack")}, {AuthenticationService.CurrentUser.FirstName}! 👋";
            }
        }

        private void UpdateLanguage()
        {
            // Update button texts
            ProfileButton.Content = $"👤 {LanguageService.GetText("Profile")}";
            LogoutButton.Content = $"🚪 {LanguageService.GetText("Logout")}";

            // Update navigation buttons
            DashboardButton.Content = $"📊 {LanguageService.GetText("Dashboard")}";
            CoursesButton.Content = $"📚 {LanguageService.GetText("Courses")}";
            VocabularyButton.Content = $"📖 {LanguageService.GetText("Vocabulary")}";
            ProgressButton.Content = $"📈 {LanguageService.GetText("Progress")}";
            AchievementsButton.Content = $"🏆 {LanguageService.GetText("Achievements")}";
            PaymentsButton.Content = $"💳 {LanguageService.GetText("Payments")}";
            AdminButton.Content = $"⚙️ {LanguageService.GetText("AdminPanel")}";

            UpdateWelcomeMessage();
        }

        private void LanguageComboBox_SelectionChanged(object? sender, Avalonia.Controls.SelectionChangedEventArgs e)
        {
            if (LanguageComboBox.SelectedIndex >= 0)
            {
                var selectedLanguage = (SupportedLanguage)LanguageComboBox.SelectedIndex;
                LanguageService.SetLanguage(selectedLanguage);
            }
        }

        private void DashboardButton_Click(object? sender, RoutedEventArgs e)
        {
            NavigateToPage(new DashboardPage());
            UpdateStatus(LanguageService.GetText("Dashboard"));
        }

        private void CoursesButton_Click(object? sender, RoutedEventArgs e)
        {
            NavigateToPage(new CoursesPage());
            UpdateStatus(LanguageService.GetText("Courses"));
        }

        private void VocabularyButton_Click(object? sender, RoutedEventArgs e)
        {
            NavigateToPage(new VocabularyPage());
            UpdateStatus(LanguageService.GetText("Vocabulary"));
        }

        private void ProgressButton_Click(object? sender, RoutedEventArgs e)
        {
            NavigateToPage(new ProgressPage());
            UpdateStatus(LanguageService.GetText("Progress"));
        }

        private void AchievementsButton_Click(object? sender, RoutedEventArgs e)
        {
            NavigateToPage(new AchievementsPage());
            UpdateStatus(LanguageService.GetText("Achievements"));
        }

        private void PaymentsButton_Click(object? sender, RoutedEventArgs e)
        {
            NavigateToPage(new PaymentsPage());
            UpdateStatus(LanguageService.GetText("Payments"));
        }

        private void AdminButton_Click(object? sender, RoutedEventArgs e)
        {
            if (AuthenticationService.IsAdmin)
            {
                NavigateToPage(new AdminPage());
                UpdateStatus("Admin Panel");
            }
            else
            {
                // Avalonia'da MessageBox yerine basit bir dialog kullanabiliriz
                UpdateStatus("Bu sayfaya erişim yetkiniz yok.");
            }
        }

        private void ProfileButton_Click(object? sender, RoutedEventArgs e)
        {
            var profileWindow = new ProfileWindow();
            profileWindow.ShowDialog(this);
        }

        private void LogoutButton_Click(object? sender, RoutedEventArgs e)
        {
            // Basit onay için şimdilik direkt çıkış yapalım
            // Gerçek uygulamada MessageBox.Show yerine custom dialog kullanılabilir
            
            AuthenticationService.Logout();
            
            var loginWindow = new LoginWindow();
            loginWindow.Show();
            this.Close();
        }

        public void NavigateToPage(UserControl page)
        {
            try
            {
                MainContent.Content = page;
            }
            catch (Exception ex)
            {
                UpdateStatus($"Page loading error: {ex.Message}");
            }
        }

        private void UpdateStatus(string status)
        {
            StatusTextBlock.Text = status;
        }

        // Public navigation methods for other pages to use
        public void NavigateToCoursesPage()
        {
            NavigateToPage(new CoursesPage());
            UpdateStatus("Dersler");
        }

        public void NavigateToProgressPage()
        {
            NavigateToPage(new ProgressPage());
            UpdateStatus("İlerleme");
        }

        public void NavigateToPaymentsPage()
        {
            NavigateToPage(new PaymentsPage());
            UpdateStatus("Ödemeler");
        }
    }
}
