using System.Windows;
using System.Windows.Controls;
using JavaOtomasyonApp.Services;
using JavaOtomasyonApp.Views.Pages;

namespace JavaOtomasyonApp.Views
{
    public partial class MainWindow : Window
    {
        public MainWindow()
        {
            InitializeComponent();
            InitializeWindow();
        }

        private void InitializeWindow()
        {
            // Show user information
            if (AuthenticationService.CurrentUser != null)
            {
                UpdateWelcomeMessage();

                // Admin panel only visible for admin users
                if (AuthenticationService.IsAdmin)
                {
                    AdminButton.Visibility = Visibility.Visible;
                }
            }

            // Show Dashboard page by default
            NavigateToPage(new DashboardPage());
            UpdateStatus("Dashboard");
        }

        private void UpdateWelcomeMessage()
        {
            if (AuthenticationService.CurrentUser != null)
            {
                WelcomeTextBlock.Text = $"Welcome back, {AuthenticationService.CurrentUser.FirstName}! 👋";
            }
        }

        private void DashboardButton_Click(object sender, RoutedEventArgs e)
        {
            NavigateToPage(new DashboardPage());
            UpdateStatus("Dashboard");
        }

        private void CoursesButton_Click(object sender, RoutedEventArgs e)
        {
            NavigateToPage(new CoursesPage());
            UpdateStatus("Courses");
        }

        private void VocabularyButton_Click(object sender, RoutedEventArgs e)
        {
            NavigateToPage(new VocabularyPage());
            UpdateStatus("Vocabulary");
        }

        private void ProgressButton_Click(object sender, RoutedEventArgs e)
        {
            NavigateToPage(new ProgressPage());
            UpdateStatus("Progress");
        }

        private void AchievementsButton_Click(object sender, RoutedEventArgs e)
        {
            NavigateToPage(new AchievementsPage());
            UpdateStatus("Achievements");
        }

        private void PaymentsButton_Click(object sender, RoutedEventArgs e)
        {
            NavigateToPage(new PaymentsPage());
            UpdateStatus("Payments");
        }

        private void AdminButton_Click(object sender, RoutedEventArgs e)
        {
            if (AuthenticationService.IsAdmin)
            {
                NavigateToPage(new AdminPage());
                UpdateStatus("Admin Panel");
            }
            else
            {
                MessageBox.Show("You don't have permission to access this page.", "Access Denied", MessageBoxButton.OK, MessageBoxImage.Warning);
                UpdateStatus("Access denied.");
            }
        }

        private void ProfileButton_Click(object sender, RoutedEventArgs e)
        {
            var profileWindow = new ProfileWindow();
            profileWindow.ShowDialog();
        }

        private void LogoutButton_Click(object sender, RoutedEventArgs e)
        {
            var result = MessageBox.Show("Are you sure you want to logout?", "Confirm Logout", MessageBoxButton.YesNo, MessageBoxImage.Question);

            if (result == MessageBoxResult.Yes)
            {
                AuthenticationService.Logout();

                var loginWindow = new LoginWindow();
                loginWindow.Show();
                this.Close();
            }
        }

        public void NavigateToPage(UserControl page)
        {
            try
            {
                MainContent.Content = page;
            }
            catch (Exception ex)
            {
                UpdateStatus($"Page loading error: {ex.Message}");
            }
        }

        private void UpdateStatus(string status)
        {
            StatusTextBlock.Text = status;
        }

        // Public navigation methods for other pages to use
        public void NavigateToCoursesPage()
        {
            NavigateToPage(new CoursesPage());
            UpdateStatus("Courses");
        }

        public void NavigateToProgressPage()
        {
            NavigateToPage(new ProgressPage());
            UpdateStatus("Progress");
        }

        public void NavigateToPaymentsPage()
        {
            NavigateToPage(new PaymentsPage());
            UpdateStatus("Payments");
        }
    }
}
