<Application xmlns="https://github.com/avaloniaui"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             x:Class="JavaOtomasyonApp.App"
             RequestedThemeVariant="Default">
             <!-- "Default" ThemeVariant follows system theme variant. "Dark" or "Light" are other available options. -->

    <Application.Styles>
        <FluentTheme />
        
        <!-- Custom Styles -->
        <Style Selector="Button.modern">
            <Setter Property="Background" Value="#007ACC"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="15,8"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="CornerRadius" Value="5"/>
        </Style>
        
        <Style Selector="Button.modern:pointerover">
            <Setter Property="Background" Value="#005A9E"/>
        </Style>
        
        <Style Selector="Button.modern:pressed">
            <Setter Property="Background" Value="#004578"/>
        </Style>
        
        <Style Selector="TextBox.modern">
            <Setter Property="Padding" Value="10,8"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="BorderBrush" Value="#CCCCCC"/>
            <Setter Property="Background" Value="White"/>
            <Setter Property="CornerRadius" Value="3"/>
        </Style>
        
        <Style Selector="TextBlock.title">
            <Setter Property="FontSize" Value="24"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Foreground" Value="#333333"/>
            <Setter Property="Margin" Value="0,0,0,20"/>
        </Style>
        
        <Style Selector="TextBlock.subtitle">
            <Setter Property="FontSize" Value="16"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="Foreground" Value="#555555"/>
            <Setter Property="Margin" Value="0,0,0,10"/>
        </Style>
        
        <Style Selector="Border.card">
            <Setter Property="Background" Value="White"/>
            <Setter Property="CornerRadius" Value="10"/>
            <Setter Property="Padding" Value="20"/>
            <Setter Property="Margin" Value="0,0,0,20"/>
            <Setter Property="BorderBrush" Value="#E0E0E0"/>
            <Setter Property="BorderThickness" Value="1"/>
        </Style>

        <!-- Modern Card Styles -->
        <Style Selector="Border.modern-card">
            <Setter Property="Background" Value="White"/>
            <Setter Property="CornerRadius" Value="15"/>
            <Setter Property="Padding" Value="25"/>
            <Setter Property="Margin" Value="0,0,0,20"/>
            <Setter Property="Effect">
                <Setter.Value>
                    <DropShadowEffect Color="Black" Opacity="0.1" BlurRadius="15" OffsetY="5"/>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- Modern Subtitle Style -->
        <Style Selector="TextBlock.modern-subtitle">
            <Setter Property="FontSize" Value="18"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Foreground" Value="#333"/>
        </Style>

        <!-- Action Button Styles -->
        <Style Selector="Button.action-button-primary">
            <Setter Property="Background">
                <Setter.Value>
                    <LinearGradientBrush StartPoint="0%,0%" EndPoint="100%,0%">
                        <GradientStop Color="#667eea" Offset="0"/>
                        <GradientStop Color="#764ba2" Offset="1"/>
                    </LinearGradientBrush>
                </Setter.Value>
            </Setter>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="20,12"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontWeight" Value="Medium"/>
            <Setter Property="CornerRadius" Value="25"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Transitions">
                <Transitions>
                    <TransformOperationsTransition Property="RenderTransform" Duration="0:0:0.2"/>
                </Transitions>
            </Setter>
        </Style>

        <Style Selector="Button.action-button-primary:pointerover">
            <Setter Property="RenderTransform" Value="scale(1.05)"/>
        </Style>

        <Style Selector="Button.action-button-success">
            <Setter Property="Background">
                <Setter.Value>
                    <LinearGradientBrush StartPoint="0%,0%" EndPoint="100%,0%">
                        <GradientStop Color="#4CAF50" Offset="0"/>
                        <GradientStop Color="#45a049" Offset="1"/>
                    </LinearGradientBrush>
                </Setter.Value>
            </Setter>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="20,12"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontWeight" Value="Medium"/>
            <Setter Property="CornerRadius" Value="25"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Transitions">
                <Transitions>
                    <TransformOperationsTransition Property="RenderTransform" Duration="0:0:0.2"/>
                </Transitions>
            </Setter>
        </Style>

        <Style Selector="Button.action-button-success:pointerover">
            <Setter Property="RenderTransform" Value="scale(1.05)"/>
        </Style>

        <Style Selector="Button.action-button-info">
            <Setter Property="Background">
                <Setter.Value>
                    <LinearGradientBrush StartPoint="0%,0%" EndPoint="100%,0%">
                        <GradientStop Color="#2196F3" Offset="0"/>
                        <GradientStop Color="#1976D2" Offset="1"/>
                    </LinearGradientBrush>
                </Setter.Value>
            </Setter>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="20,12"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontWeight" Value="Medium"/>
            <Setter Property="CornerRadius" Value="25"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Transitions">
                <Transitions>
                    <TransformOperationsTransition Property="RenderTransform" Duration="0:0:0.2"/>
                </Transitions>
            </Setter>
        </Style>

        <Style Selector="Button.action-button-info:pointerover">
            <Setter Property="RenderTransform" Value="scale(1.05)"/>
        </Style>

        <!-- Activity Card Style -->
        <Style Selector="Border.activity-card">
            <Setter Property="Background" Value="White"/>
            <Setter Property="CornerRadius" Value="12"/>
            <Setter Property="Padding" Value="15"/>
            <Setter Property="Effect">
                <Setter.Value>
                    <DropShadowEffect Color="Black" Opacity="0.05" BlurRadius="8" OffsetY="2"/>
                </Setter.Value>
            </Setter>
            <Setter Property="Transitions">
                <Transitions>
                    <TransformOperationsTransition Property="RenderTransform" Duration="0:0:0.2"/>
                </Transitions>
            </Setter>
        </Style>

        <Style Selector="Border.activity-card:pointerover">
            <Setter Property="RenderTransform" Value="scale(1.02)"/>
        </Style>

        <!-- Course Card Style -->
        <Style Selector="Border.course-card">
            <Setter Property="Background" Value="White"/>
            <Setter Property="CornerRadius" Value="15"/>
            <Setter Property="Padding" Value="20"/>
            <Setter Property="Effect">
                <Setter.Value>
                    <DropShadowEffect Color="Black" Opacity="0.1" BlurRadius="15" OffsetY="5"/>
                </Setter.Value>
            </Setter>
            <Setter Property="Transitions">
                <Transitions>
                    <TransformOperationsTransition Property="RenderTransform" Duration="0:0:0.3"/>
                </Transitions>
            </Setter>
        </Style>

        <Style Selector="Border.course-card:pointerover">
            <Setter Property="RenderTransform" Value="scale(1.02)"/>
        </Style>

        <!-- Category Chip Style -->
        <Style Selector="Border.category-chip">
            <Setter Property="Background" Value="White"/>
            <Setter Property="CornerRadius" Value="20"/>
            <Setter Property="Padding" Value="12,8"/>
            <Setter Property="BorderBrush" Value="#E0E0E0"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="Transitions">
                <Transitions>
                    <BrushTransition Property="Background" Duration="0:0:0.2"/>
                    <TransformOperationsTransition Property="RenderTransform" Duration="0:0:0.2"/>
                </Transitions>
            </Setter>
        </Style>

        <Style Selector="Border.category-chip:pointerover">
            <Setter Property="Background" Value="#F0F8FF"/>
            <Setter Property="RenderTransform" Value="scale(1.05)"/>
        </Style>

        <!-- Word Card Style -->
        <Style Selector="Border.word-card">
            <Setter Property="Background" Value="White"/>
            <Setter Property="CornerRadius" Value="12"/>
            <Setter Property="Padding" Value="15"/>
            <Setter Property="BorderBrush" Value="#E0E0E0"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="Transitions">
                <Transitions>
                    <TransformOperationsTransition Property="RenderTransform" Duration="0:0:0.2"/>
                </Transitions>
            </Setter>
        </Style>

        <Style Selector="Border.word-card:pointerover">
            <Setter Property="RenderTransform" Value="scale(1.02)"/>
        </Style>

        <!-- Achievement Card Style -->
        <Style Selector="Border.achievement-card">
            <Setter Property="Background" Value="White"/>
            <Setter Property="CornerRadius" Value="15"/>
            <Setter Property="Padding" Value="20"/>
            <Setter Property="Effect">
                <Setter.Value>
                    <DropShadowEffect Color="Black" Opacity="0.1" BlurRadius="10" OffsetY="3"/>
                </Setter.Value>
            </Setter>
            <Setter Property="Transitions">
                <Transitions>
                    <TransformOperationsTransition Property="RenderTransform" Duration="0:0:0.2"/>
                </Transitions>
            </Setter>
        </Style>

        <Style Selector="Border.achievement-card:pointerover">
            <Setter Property="RenderTransform" Value="scale(1.02)"/>
        </Style>

        <!-- Category Filter Button Style -->
        <Style Selector="Button.category-filter-button">
            <Setter Property="Background" Value="White"/>
            <Setter Property="Foreground" Value="#666"/>
            <Setter Property="BorderBrush" Value="#E0E0E0"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="Padding" Value="15,8"/>
            <Setter Property="FontSize" Value="13"/>
            <Setter Property="FontWeight" Value="Medium"/>
            <Setter Property="CornerRadius" Value="20"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Transitions">
                <Transitions>
                    <BrushTransition Property="Background" Duration="0:0:0.2"/>
                    <BrushTransition Property="Foreground" Duration="0:0:0.2"/>
                </Transitions>
            </Setter>
        </Style>

        <Style Selector="Button.category-filter-button:pointerover">
            <Setter Property="Background" Value="#667eea"/>
            <Setter Property="Foreground" Value="White"/>
        </Style>

        <!-- Icon Button Style -->
        <Style Selector="Button.icon-button">
            <Setter Property="Background" Value="Transparent"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="8"/>
            <Setter Property="FontSize" Value="16"/>
            <Setter Property="CornerRadius" Value="15"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Transitions">
                <Transitions>
                    <BrushTransition Property="Background" Duration="0:0:0.2"/>
                </Transitions>
            </Setter>
        </Style>

        <Style Selector="Button.icon-button:pointerover">
            <Setter Property="Background" Value="#F0F0F0"/>
        </Style>

        <!-- Modern Login TextBox Style -->
        <Style Selector="TextBox.modern-login">
            <Setter Property="Padding" Value="15,12"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="BorderThickness" Value="2"/>
            <Setter Property="BorderBrush" Value="#E0E0E0"/>
            <Setter Property="Background" Value="White"/>
            <Setter Property="CornerRadius" Value="10"/>
            <Setter Property="Transitions">
                <Transitions>
                    <BrushTransition Property="BorderBrush" Duration="0:0:0.2"/>
                </Transitions>
            </Setter>
        </Style>

        <Style Selector="TextBox.modern-login:focus">
            <Setter Property="BorderBrush" Value="#667eea"/>
        </Style>

        <!-- Login Button Style -->
        <Style Selector="Button.login-button">
            <Setter Property="Background">
                <Setter.Value>
                    <LinearGradientBrush StartPoint="0%,0%" EndPoint="100%,0%">
                        <GradientStop Color="#667eea" Offset="0"/>
                        <GradientStop Color="#764ba2" Offset="1"/>
                    </LinearGradientBrush>
                </Setter.Value>
            </Setter>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="20,15"/>
            <Setter Property="FontSize" Value="16"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="CornerRadius" Value="25"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="HorizontalAlignment" Value="Stretch"/>
            <Setter Property="Transitions">
                <Transitions>
                    <TransformOperationsTransition Property="RenderTransform" Duration="0:0:0.2"/>
                </Transitions>
            </Setter>
        </Style>

        <Style Selector="Button.login-button:pointerover">
            <Setter Property="RenderTransform" Value="scale(1.02)"/>
        </Style>

        <Style Selector="Button.login-button:pressed">
            <Setter Property="RenderTransform" Value="scale(0.98)"/>
        </Style>

        <!-- Quiz Selection Card Style -->
        <Style Selector="Border.quiz-selection-card">
            <Setter Property="Background" Value="White"/>
            <Setter Property="CornerRadius" Value="15"/>
            <Setter Property="Padding" Value="20"/>
            <Setter Property="BorderBrush" Value="#E0E0E0"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="Effect">
                <Setter.Value>
                    <DropShadowEffect Color="Black" Opacity="0.05" BlurRadius="10" OffsetY="3"/>
                </Setter.Value>
            </Setter>
            <Setter Property="Transitions">
                <Transitions>
                    <TransformOperationsTransition Property="RenderTransform" Duration="0:0:0.2"/>
                    <BrushTransition Property="BorderBrush" Duration="0:0:0.2"/>
                </Transitions>
            </Setter>
        </Style>

        <Style Selector="Border.quiz-selection-card:pointerover">
            <Setter Property="RenderTransform" Value="scale(1.02)"/>
            <Setter Property="BorderBrush" Value="#667eea"/>
        </Style>

        <!-- Radio Button Style for Quiz -->
        <Style Selector="RadioButton">
            <Setter Property="Background" Value="White"/>
            <Setter Property="BorderBrush" Value="#E0E0E0"/>
            <Setter Property="BorderThickness" Value="2"/>
            <Setter Property="CornerRadius" Value="10"/>
            <Setter Property="Transitions">
                <Transitions>
                    <BrushTransition Property="Background" Duration="0:0:0.2"/>
                    <BrushTransition Property="BorderBrush" Duration="0:0:0.2"/>
                </Transitions>
            </Setter>
        </Style>

        <Style Selector="RadioButton:pointerover">
            <Setter Property="Background" Value="#F8F9FA"/>
            <Setter Property="BorderBrush" Value="#667eea"/>
        </Style>

        <Style Selector="RadioButton:checked">
            <Setter Property="Background" Value="#E3F2FD"/>
            <Setter Property="BorderBrush" Value="#2196F3"/>
        </Style>
    </Application.Styles>
</Application>
