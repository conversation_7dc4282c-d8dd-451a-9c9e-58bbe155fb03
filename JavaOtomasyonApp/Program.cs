using Avalonia;
using Avalonia.Controls.ApplicationLifetimes;
using Avalonia.Markup.Xaml;
using JavaOtomasyonApp.Data;
using JavaOtomasyonApp.Models;
using JavaOtomasyonApp.Helpers;
using JavaOtomasyonApp.Views;
using Microsoft.EntityFrameworkCore;

namespace JavaOtomasyonApp
{
    public partial class App : Application
    {
        public override void Initialize()
        {
            AvaloniaXamlLoader.Load(this);
        }

        public override void OnFrameworkInitializationCompleted()
        {
            if (ApplicationLifetime is IClassicDesktopStyleApplicationLifetime desktop)
            {
                // Hata yönetimini başlat
                ErrorHandler.Initialize();

                // Veritabanını başlat
                InitializeDatabase().Wait();

                desktop.MainWindow = new LoginWindow();
            }

            base.OnFrameworkInitializationCompleted();
        }

        private static async Task InitializeDatabase()
        {
            try
            {
                using var context = new AppDbContext();
                await context.Database.EnsureCreatedAsync();

                // Varsayılan admin kullanıcısı oluştur
                if (!await context.Users.AnyAsync(u => u.Role == "Admin"))
                {
                    var adminUser = new User
                    {
                        Email = "<EMAIL>",
                        PasswordHash = BCrypt.Net.BCrypt.HashPassword("admin123"),
                        FirstName = "Admin",
                        LastName = "User",
                        Role = "Admin",
                        IsActive = true,
                        CreatedDate = DateTime.Now
                    };
                    context.Users.Add(adminUser);
                    await context.SaveChangesAsync();
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Veritabanı hatası: {ex.Message}");
            }
        }
    }

    class Program
    {
        [STAThread]
        public static void Main(string[] args) => BuildAvaloniaApp()
            .StartWithClassicDesktopLifetime(args);

        public static AppBuilder BuildAvaloniaApp()
            => AppBuilder.Configure<App>()
                .UsePlatformDetect()
                .WithInterFont()
                .LogToTrace();
    }
}
