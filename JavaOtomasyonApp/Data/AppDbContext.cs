using Microsoft.EntityFrameworkCore;
using JavaOtomasyonApp.Models;

namespace JavaOtomasyonApp.Data
{
    public class AppDbContext : DbContext
    {
        public DbSet<User> Users { get; set; }
        public DbSet<Course> Courses { get; set; }
        public DbSet<Payment> Payments { get; set; }
        public DbSet<UserProgress> UserProgresses { get; set; }
        public DbSet<Lesson> Lessons { get; set; }
        public DbSet<Quiz> Quizzes { get; set; }
        public DbSet<Question> Questions { get; set; }
        public DbSet<Answer> Answers { get; set; }
        public DbSet<VocabularyWord> VocabularyWords { get; set; }
        public DbSet<UserVocabulary> UserVocabularies { get; set; }
        public DbSet<Achievement> Achievements { get; set; }
        public DbSet<UserAchievement> UserAchievements { get; set; }
        public DbSet<LessonProgress> LessonProgresses { get; set; }
        public DbSet<QuizAttempt> QuizAttempts { get; set; }
        public DbSet<QuizAnswer> QuizAnswers { get; set; }

        public AppDbContext() : base()
        {
        }

        public AppDbContext(DbContextOptions<AppDbContext> options) : base(options)
        {
        }

        protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
        {
            var dbPath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.LocalApplicationData),
                                    "EnglishLearningApp", "database.db");
            
            // Klasör yoksa oluştur
            var directory = Path.GetDirectoryName(dbPath);
            if (!Directory.Exists(directory))
            {
                Directory.CreateDirectory(directory!);
            }

            optionsBuilder.UseSqlite($"Data Source={dbPath}");
        }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

            // User entity configuration
            modelBuilder.Entity<User>(entity =>
            {
                entity.HasIndex(e => e.Email).IsUnique();
                entity.Property(e => e.Email).IsRequired();
                entity.Property(e => e.PasswordHash).IsRequired();
                entity.Property(e => e.Role).HasDefaultValue("User");
                entity.Property(e => e.IsActive).HasDefaultValue(true);
                entity.Property(e => e.CreatedDate).HasDefaultValueSql("datetime('now')");
            });

            // Course entity configuration
            modelBuilder.Entity<Course>(entity =>
            {
                entity.Property(e => e.Price).HasColumnType("decimal(10,2)");
                entity.Property(e => e.IsActive).HasDefaultValue(true);
                entity.Property(e => e.CreatedDate).HasDefaultValueSql("datetime('now')");
                entity.Property(e => e.Level).HasConversion<int>();
            });

            // Payment entity configuration
            modelBuilder.Entity<Payment>(entity =>
            {
                entity.Property(e => e.Amount).HasColumnType("decimal(10,2)");
                entity.Property(e => e.Status).HasConversion<int>();
                entity.Property(e => e.PaymentDate).HasDefaultValueSql("datetime('now')");
                
                entity.HasOne(d => d.User)
                    .WithMany(p => p.Payments)
                    .HasForeignKey(d => d.UserId)
                    .OnDelete(DeleteBehavior.Cascade);

                entity.HasOne(d => d.Course)
                    .WithMany(p => p.Payments)
                    .HasForeignKey(d => d.CourseId)
                    .OnDelete(DeleteBehavior.Cascade);
            });

            // UserProgress entity configuration
            modelBuilder.Entity<UserProgress>(entity =>
            {
                entity.Property(e => e.Status).HasConversion<int>();
                entity.Property(e => e.ProgressPercentage).HasDefaultValue(0);
                entity.Property(e => e.TimeSpentMinutes).HasDefaultValue(0);

                entity.HasOne(d => d.User)
                    .WithMany(p => p.UserProgresses)
                    .HasForeignKey(d => d.UserId)
                    .OnDelete(DeleteBehavior.Cascade);

                entity.HasOne(d => d.Course)
                    .WithMany(p => p.UserProgresses)
                    .HasForeignKey(d => d.CourseId)
                    .OnDelete(DeleteBehavior.Cascade);

                // Bir kullanıcı bir kursa sadece bir kez kayıt olabilir
                entity.HasIndex(e => new { e.UserId, e.CourseId }).IsUnique();
            });

            // Lesson entity configuration
            modelBuilder.Entity<Lesson>(entity =>
            {
                entity.Property(e => e.Type).HasConversion<int>();
                entity.HasOne(d => d.Course)
                    .WithMany(p => p.Lessons)
                    .HasForeignKey(d => d.CourseId)
                    .OnDelete(DeleteBehavior.Cascade);
            });

            // Quiz entity configuration
            modelBuilder.Entity<Quiz>(entity =>
            {
                entity.HasOne(d => d.Lesson)
                    .WithMany(p => p.Quizzes)
                    .HasForeignKey(d => d.LessonId)
                    .OnDelete(DeleteBehavior.Cascade);
            });

            // Question entity configuration
            modelBuilder.Entity<Question>(entity =>
            {
                entity.Property(e => e.Type).HasConversion<int>();
                entity.HasOne(d => d.Quiz)
                    .WithMany(p => p.Questions)
                    .HasForeignKey(d => d.QuizId)
                    .OnDelete(DeleteBehavior.Cascade);
            });

            // Answer entity configuration
            modelBuilder.Entity<Answer>(entity =>
            {
                entity.HasOne(d => d.Question)
                    .WithMany(p => p.Answers)
                    .HasForeignKey(d => d.QuestionId)
                    .OnDelete(DeleteBehavior.Cascade);
            });

            // VocabularyWord entity configuration
            modelBuilder.Entity<VocabularyWord>(entity =>
            {
                entity.Property(e => e.Difficulty).HasConversion<int>();
                entity.Property(e => e.PartOfSpeech).HasConversion<int>();
                entity.HasIndex(e => e.EnglishWord);
            });

            // UserVocabulary entity configuration
            modelBuilder.Entity<UserVocabulary>(entity =>
            {
                entity.HasOne(d => d.User)
                    .WithMany()
                    .HasForeignKey(d => d.UserId)
                    .OnDelete(DeleteBehavior.Cascade);

                entity.HasOne(d => d.VocabularyWord)
                    .WithMany(p => p.UserVocabularies)
                    .HasForeignKey(d => d.VocabularyWordId)
                    .OnDelete(DeleteBehavior.Cascade);

                entity.HasIndex(e => new { e.UserId, e.VocabularyWordId }).IsUnique();
            });

            // Achievement entity configuration
            modelBuilder.Entity<Achievement>(entity =>
            {
                entity.Property(e => e.Type).HasConversion<int>();
            });

            // UserAchievement entity configuration
            modelBuilder.Entity<UserAchievement>(entity =>
            {
                entity.HasOne(d => d.User)
                    .WithMany()
                    .HasForeignKey(d => d.UserId)
                    .OnDelete(DeleteBehavior.Cascade);

                entity.HasOne(d => d.Achievement)
                    .WithMany(p => p.UserAchievements)
                    .HasForeignKey(d => d.AchievementId)
                    .OnDelete(DeleteBehavior.Cascade);

                entity.HasIndex(e => new { e.UserId, e.AchievementId }).IsUnique();
            });

            // LessonProgress entity configuration
            modelBuilder.Entity<LessonProgress>(entity =>
            {
                entity.Property(e => e.Status).HasConversion<int>();
                entity.HasOne(d => d.User)
                    .WithMany()
                    .HasForeignKey(d => d.UserId)
                    .OnDelete(DeleteBehavior.Cascade);

                entity.HasOne(d => d.Lesson)
                    .WithMany(p => p.LessonProgresses)
                    .HasForeignKey(d => d.LessonId)
                    .OnDelete(DeleteBehavior.Cascade);

                entity.HasIndex(e => new { e.UserId, e.LessonId }).IsUnique();
            });

            // QuizAttempt entity configuration
            modelBuilder.Entity<QuizAttempt>(entity =>
            {
                entity.HasOne(d => d.User)
                    .WithMany()
                    .HasForeignKey(d => d.UserId)
                    .OnDelete(DeleteBehavior.Cascade);

                entity.HasOne(d => d.Quiz)
                    .WithMany(p => p.QuizAttempts)
                    .HasForeignKey(d => d.QuizId)
                    .OnDelete(DeleteBehavior.Cascade);
            });

            // QuizAnswer entity configuration
            modelBuilder.Entity<QuizAnswer>(entity =>
            {
                entity.HasOne(d => d.QuizAttempt)
                    .WithMany(p => p.QuizAnswers)
                    .HasForeignKey(d => d.QuizAttemptId)
                    .OnDelete(DeleteBehavior.Cascade);

                entity.HasOne(d => d.Question)
                    .WithMany()
                    .HasForeignKey(d => d.QuestionId)
                    .OnDelete(DeleteBehavior.Cascade);
            });

            // Seed data
            SeedData(modelBuilder);
        }

        private void SeedData(ModelBuilder modelBuilder)
        {
            // İngilizce öğrenme kursları
            modelBuilder.Entity<Course>().HasData(
                new Course
                {
                    Id = 1,
                    Title = "Temel İngilizce Gramer",
                    Description = "İngilizce dilbilgisinin temel kuralları ve yapıları",
                    Content = "Bu kursta İngilizce dilbilgisinin temellerini öğreneceksiniz...",
                    Level = CourseLevel.Beginner,
                    Type = CourseType.Grammar,
                    Price = 99.99m,
                    OrderIndex = 1,
                    EstimatedDurationMinutes = 120,
                    Prerequisites = "Herhangi bir ön bilgi gerekmez",
                    Color = "#4CAF50"
                },
                new Course
                {
                    Id = 2,
                    Title = "Günlük Hayat Kelimeleri",
                    Description = "Günlük yaşamda kullanılan temel İngilizce kelimeler",
                    Content = "Bu kursta günlük hayatta en çok kullanılan İngilizce kelimeleri öğreneceksiniz...",
                    Level = CourseLevel.Beginner,
                    Type = CourseType.Vocabulary,
                    Price = 79.99m,
                    OrderIndex = 2,
                    EstimatedDurationMinutes = 90,
                    Prerequisites = "Herhangi bir ön bilgi gerekmez",
                    Color = "#2196F3"
                },
                new Course
                {
                    Id = 3,
                    Title = "İngilizce Konuşma Pratiği",
                    Description = "Temel konuşma becerileri ve telaffuz çalışmaları",
                    Content = "Bu kursta İngilizce konuşma becerilerinizi geliştirip telaffuzunuzu iyileştireceksiniz...",
                    Level = CourseLevel.Intermediate,
                    Type = CourseType.Speaking,
                    Price = 149.99m,
                    OrderIndex = 3,
                    EstimatedDurationMinutes = 180,
                    Prerequisites = "Temel İngilizce Gramer kursunu tamamlamış olmak",
                    Color = "#FF9800"
                },
                new Course
                {
                    Id = 4,
                    Title = "İngilizce Dinleme Becerileri",
                    Description = "Dinleme anlama ve ses tanıma çalışmaları",
                    Content = "Bu kursta İngilizce dinleme becerilerinizi geliştirip farklı aksanları anlayabileceksiniz...",
                    Level = CourseLevel.Intermediate,
                    Type = CourseType.Listening,
                    Price = 129.99m,
                    OrderIndex = 4,
                    EstimatedDurationMinutes = 150,
                    Prerequisites = "Günlük Hayat Kelimeleri kursunu tamamlamış olmak",
                    Color = "#9C27B0"
                },
                new Course
                {
                    Id = 5,
                    Title = "İş İngilizcesi",
                    Description = "İş hayatında kullanılan İngilizce ifadeler ve terminoloji",
                    Content = "Bu kursta iş hayatında kullanılan İngilizce ifadeleri ve profesyonel terminolojiyi öğreneceksiniz...",
                    Level = CourseLevel.Advanced,
                    Type = CourseType.Vocabulary,
                    Price = 199.99m,
                    OrderIndex = 5,
                    EstimatedDurationMinutes = 200,
                    Prerequisites = "İngilizce Konuşma Pratiği kursunu tamamlamış olmak",
                    Color = "#607D8B"
                }
            );

            // Dersler (Lessons)
            modelBuilder.Entity<Lesson>().HasData(
                // Temel İngilizce Gramer dersleri
                new Lesson
                {
                    Id = 1,
                    CourseId = 1,
                    Title = "Present Simple Tense",
                    Content = "Learn how to use present simple tense for daily activities and facts.",
                    Type = LessonType.Text,
                    OrderIndex = 1,
                    EstimatedDurationMinutes = 20
                },
                new Lesson
                {
                    Id = 2,
                    CourseId = 1,
                    Title = "Articles: A, An, The",
                    Content = "Understanding when and how to use articles in English.",
                    Type = LessonType.Interactive,
                    OrderIndex = 2,
                    EstimatedDurationMinutes = 15
                },
                new Lesson
                {
                    Id = 3,
                    CourseId = 1,
                    Title = "Plural Nouns",
                    Content = "Rules for making nouns plural in English.",
                    Type = LessonType.Text,
                    OrderIndex = 3,
                    EstimatedDurationMinutes = 18
                },

                // Günlük Hayat Kelimeleri dersleri
                new Lesson
                {
                    Id = 4,
                    CourseId = 2,
                    Title = "Family Members",
                    Content = "Learn vocabulary related to family relationships.",
                    Type = LessonType.Interactive,
                    OrderIndex = 1,
                    EstimatedDurationMinutes = 15
                },
                new Lesson
                {
                    Id = 5,
                    CourseId = 2,
                    Title = "Colors and Shapes",
                    Content = "Basic colors and geometric shapes vocabulary.",
                    Type = LessonType.Interactive,
                    OrderIndex = 2,
                    EstimatedDurationMinutes = 12
                },
                new Lesson
                {
                    Id = 6,
                    CourseId = 2,
                    Title = "Food and Drinks",
                    Content = "Common food and beverage vocabulary for daily use.",
                    Type = LessonType.Interactive,
                    OrderIndex = 3,
                    EstimatedDurationMinutes = 20
                }
            );

            // Kelimeler (Vocabulary Words)
            modelBuilder.Entity<VocabularyWord>().HasData(
                // Aile üyeleri
                new VocabularyWord
                {
                    Id = 1,
                    EnglishWord = "mother",
                    TurkishMeaning = "anne",
                    Pronunciation = "/ˈmʌðər/",
                    ExampleSentence = "My mother is a teacher.",
                    ExampleSentenceTurkish = "Annem bir öğretmendir.",
                    Difficulty = WordDifficulty.Easy,
                    PartOfSpeech = PartOfSpeech.Noun,
                    Category = "Family"
                },
                new VocabularyWord
                {
                    Id = 2,
                    EnglishWord = "father",
                    TurkishMeaning = "baba",
                    Pronunciation = "/ˈfɑːðər/",
                    ExampleSentence = "My father works in an office.",
                    ExampleSentenceTurkish = "Babam bir ofiste çalışır.",
                    Difficulty = WordDifficulty.Easy,
                    PartOfSpeech = PartOfSpeech.Noun,
                    Category = "Family"
                },
                new VocabularyWord
                {
                    Id = 3,
                    EnglishWord = "brother",
                    TurkishMeaning = "erkek kardeş",
                    Pronunciation = "/ˈbrʌðər/",
                    ExampleSentence = "I have one brother.",
                    ExampleSentenceTurkish = "Bir erkek kardeşim var.",
                    Difficulty = WordDifficulty.Easy,
                    PartOfSpeech = PartOfSpeech.Noun,
                    Category = "Family"
                },
                new VocabularyWord
                {
                    Id = 4,
                    EnglishWord = "sister",
                    TurkishMeaning = "kız kardeş",
                    Pronunciation = "/ˈsɪstər/",
                    ExampleSentence = "My sister is younger than me.",
                    ExampleSentenceTurkish = "Kız kardeşim benden küçük.",
                    Difficulty = WordDifficulty.Easy,
                    PartOfSpeech = PartOfSpeech.Noun,
                    Category = "Family"
                },

                // Renkler
                new VocabularyWord
                {
                    Id = 5,
                    EnglishWord = "red",
                    TurkishMeaning = "kırmızı",
                    Pronunciation = "/red/",
                    ExampleSentence = "The apple is red.",
                    ExampleSentenceTurkish = "Elma kırmızıdır.",
                    Difficulty = WordDifficulty.Easy,
                    PartOfSpeech = PartOfSpeech.Adjective,
                    Category = "Colors"
                },
                new VocabularyWord
                {
                    Id = 6,
                    EnglishWord = "blue",
                    TurkishMeaning = "mavi",
                    Pronunciation = "/bluː/",
                    ExampleSentence = "The sky is blue.",
                    ExampleSentenceTurkish = "Gökyüzü mavidir.",
                    Difficulty = WordDifficulty.Easy,
                    PartOfSpeech = PartOfSpeech.Adjective,
                    Category = "Colors"
                },
                new VocabularyWord
                {
                    Id = 7,
                    EnglishWord = "green",
                    TurkishMeaning = "yeşil",
                    Pronunciation = "/ɡriːn/",
                    ExampleSentence = "Grass is green.",
                    ExampleSentenceTurkish = "Çimen yeşildir.",
                    Difficulty = WordDifficulty.Easy,
                    PartOfSpeech = PartOfSpeech.Adjective,
                    Category = "Colors"
                },

                // Yiyecekler
                new VocabularyWord
                {
                    Id = 8,
                    EnglishWord = "apple",
                    TurkishMeaning = "elma",
                    Pronunciation = "/ˈæpəl/",
                    ExampleSentence = "I eat an apple every day.",
                    ExampleSentenceTurkish = "Her gün bir elma yerim.",
                    Difficulty = WordDifficulty.Easy,
                    PartOfSpeech = PartOfSpeech.Noun,
                    Category = "Food"
                },
                new VocabularyWord
                {
                    Id = 9,
                    EnglishWord = "bread",
                    TurkishMeaning = "ekmek",
                    Pronunciation = "/bred/",
                    ExampleSentence = "We buy fresh bread from the bakery.",
                    ExampleSentenceTurkish = "Fırından taze ekmek alırız.",
                    Difficulty = WordDifficulty.Easy,
                    PartOfSpeech = PartOfSpeech.Noun,
                    Category = "Food"
                },
                new VocabularyWord
                {
                    Id = 10,
                    EnglishWord = "water",
                    TurkishMeaning = "su",
                    Pronunciation = "/ˈwɔːtər/",
                    ExampleSentence = "Drink plenty of water every day.",
                    ExampleSentenceTurkish = "Her gün bol su için.",
                    Difficulty = WordDifficulty.Easy,
                    PartOfSpeech = PartOfSpeech.Noun,
                    Category = "Drinks"
                }
            );

            // Quiz'ler
            modelBuilder.Entity<Quiz>().HasData(
                new Quiz
                {
                    Id = 1,
                    LessonId = 1, // Present Simple Tense
                    Title = "Present Simple Quiz",
                    Description = "Test your knowledge of present simple tense",
                    PassingScore = 70,
                    TimeLimit = 10
                },
                new Quiz
                {
                    Id = 2,
                    LessonId = 4, // Family Members
                    Title = "Family Vocabulary Quiz",
                    Description = "Test your knowledge of family member vocabulary",
                    PassingScore = 80,
                    TimeLimit = 8
                }
            );

            // Sorular (Questions)
            modelBuilder.Entity<Question>().HasData(
                // Present Simple Quiz Questions
                new Question
                {
                    Id = 1,
                    QuizId = 1,
                    QuestionText = "Which sentence uses present simple correctly?",
                    Type = QuestionType.MultipleChoice,
                    OrderIndex = 1,
                    Points = 10
                },
                new Question
                {
                    Id = 2,
                    QuizId = 1,
                    QuestionText = "She _____ to school every day.",
                    Type = QuestionType.MultipleChoice,
                    OrderIndex = 2,
                    Points = 10
                },
                new Question
                {
                    Id = 3,
                    QuizId = 1,
                    QuestionText = "Do you _____ English?",
                    Type = QuestionType.MultipleChoice,
                    OrderIndex = 3,
                    Points = 10
                },

                // Family Vocabulary Quiz Questions
                new Question
                {
                    Id = 4,
                    QuizId = 2,
                    QuestionText = "What is the Turkish meaning of 'mother'?",
                    Type = QuestionType.MultipleChoice,
                    OrderIndex = 1,
                    Points = 10
                },
                new Question
                {
                    Id = 5,
                    QuizId = 2,
                    QuestionText = "Which word means 'erkek kardeş' in English?",
                    Type = QuestionType.MultipleChoice,
                    OrderIndex = 2,
                    Points = 10
                }
            );

            // Cevaplar (Answers)
            modelBuilder.Entity<Answer>().HasData(
                // Question 1 answers
                new Answer { Id = 1, QuestionId = 1, AnswerText = "I am go to work.", IsCorrect = false, OrderIndex = 1 },
                new Answer { Id = 2, QuestionId = 1, AnswerText = "I go to work.", IsCorrect = true, OrderIndex = 2 },
                new Answer { Id = 3, QuestionId = 1, AnswerText = "I going to work.", IsCorrect = false, OrderIndex = 3 },
                new Answer { Id = 4, QuestionId = 1, AnswerText = "I goes to work.", IsCorrect = false, OrderIndex = 4 },

                // Question 2 answers
                new Answer { Id = 5, QuestionId = 2, AnswerText = "go", IsCorrect = false, OrderIndex = 1 },
                new Answer { Id = 6, QuestionId = 2, AnswerText = "goes", IsCorrect = true, OrderIndex = 2 },
                new Answer { Id = 7, QuestionId = 2, AnswerText = "going", IsCorrect = false, OrderIndex = 3 },
                new Answer { Id = 8, QuestionId = 2, AnswerText = "gone", IsCorrect = false, OrderIndex = 4 },

                // Question 3 answers
                new Answer { Id = 9, QuestionId = 3, AnswerText = "speaks", IsCorrect = false, OrderIndex = 1 },
                new Answer { Id = 10, QuestionId = 3, AnswerText = "speak", IsCorrect = true, OrderIndex = 2 },
                new Answer { Id = 11, QuestionId = 3, AnswerText = "speaking", IsCorrect = false, OrderIndex = 3 },
                new Answer { Id = 12, QuestionId = 3, AnswerText = "spoke", IsCorrect = false, OrderIndex = 4 },

                // Question 4 answers
                new Answer { Id = 13, QuestionId = 4, AnswerText = "baba", IsCorrect = false, OrderIndex = 1 },
                new Answer { Id = 14, QuestionId = 4, AnswerText = "anne", IsCorrect = true, OrderIndex = 2 },
                new Answer { Id = 15, QuestionId = 4, AnswerText = "kardeş", IsCorrect = false, OrderIndex = 3 },
                new Answer { Id = 16, QuestionId = 4, AnswerText = "çocuk", IsCorrect = false, OrderIndex = 4 },

                // Question 5 answers
                new Answer { Id = 17, QuestionId = 5, AnswerText = "sister", IsCorrect = false, OrderIndex = 1 },
                new Answer { Id = 18, QuestionId = 5, AnswerText = "brother", IsCorrect = true, OrderIndex = 2 },
                new Answer { Id = 19, QuestionId = 5, AnswerText = "father", IsCorrect = false, OrderIndex = 3 },
                new Answer { Id = 20, QuestionId = 5, AnswerText = "mother", IsCorrect = false, OrderIndex = 4 }
            );

            // Başarılar (Achievements)
            modelBuilder.Entity<Achievement>().HasData(
                new Achievement
                {
                    Id = 1,
                    Name = "First Steps",
                    Description = "Complete your first lesson",
                    Type = AchievementType.LessonCompletion,
                    Icon = "🎯",
                    Color = "#4CAF50",
                    RequiredValue = 1,
                    Points = 10
                },
                new Achievement
                {
                    Id = 2,
                    Name = "Word Master",
                    Description = "Learn 50 vocabulary words",
                    Type = AchievementType.VocabularyMastery,
                    Icon = "📖",
                    Color = "#2196F3",
                    RequiredValue = 50,
                    Points = 50
                },
                new Achievement
                {
                    Id = 3,
                    Name = "Course Completer",
                    Description = "Complete your first course",
                    Type = AchievementType.CourseCompletion,
                    Icon = "🏆",
                    Color = "#FF9800",
                    RequiredValue = 1,
                    Points = 100
                },
                new Achievement
                {
                    Id = 4,
                    Name = "Study Streak",
                    Description = "Study for 7 consecutive days",
                    Type = AchievementType.StudyStreak,
                    Icon = "🔥",
                    Color = "#F44336",
                    RequiredValue = 7,
                    Points = 75
                },
                new Achievement
                {
                    Id = 5,
                    Name = "Time Keeper",
                    Description = "Spend 10 hours studying",
                    Type = AchievementType.TimeSpent,
                    Icon = "⏰",
                    Color = "#9C27B0",
                    RequiredValue = 600, // 10 hours in minutes
                    Points = 150
                }
            );
        }
    }
}
