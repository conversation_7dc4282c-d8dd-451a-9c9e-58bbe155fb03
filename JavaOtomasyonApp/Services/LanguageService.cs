using System.Globalization;

namespace JavaOtomasyonApp.Services
{
    public enum SupportedLanguage
    {
        Turkish,
        English
    }

    public static class LanguageService
    {
        private static SupportedLanguage _currentLanguage = SupportedLanguage.English;
        private static readonly Dictionary<string, Dictionary<SupportedLanguage, string>> _translations = new();

        public static SupportedLanguage CurrentLanguage
        {
            get => _currentLanguage;
            set
            {
                _currentLanguage = value;
                LanguageChanged?.Invoke();
            }
        }

        public static event Action? LanguageChanged;

        static LanguageService()
        {
            InitializeTranslations();
        }

        private static void InitializeTranslations()
        {
            // Navigation
            AddTranslation("Dashboard", "Dashboard", "Kontrol Paneli");
            AddTranslation("Courses", "Courses", "Kurslar");
            AddTranslation("Vocabulary", "Vocabulary", "Kelimeler");
            AddTranslation("Progress", "Progress", "İlerleme");
            AddTranslation("Achievements", "Achievements", "<PERSON>şarılar");
            AddTranslation("Payments", "Payments", "Ödemeler");
            AddTranslation("Profile", "Profile", "Profil");
            AddTranslation("Logout", "Logout", "<PERSON>ıkış");
            AddTranslation("AdminPanel", "Admin Panel", "Yönetici Paneli");

            // Common
            AddTranslation("Welcome", "Welcome", "Hoş Geldiniz");
            AddTranslation("Login", "Login", "Giriş");
            AddTranslation("Register", "Register", "Kayıt Ol");
            AddTranslation("Email", "Email", "E-posta");
            AddTranslation("Password", "Password", "Şifre");
            AddTranslation("SignIn", "Sign In", "Giriş Yap");
            AddTranslation("SignUp", "Sign Up", "Kayıt Ol");
            AddTranslation("ForgotPassword", "Forgot Password?", "Şifremi Unuttum?");
            AddTranslation("Save", "Save", "Kaydet");
            AddTranslation("Cancel", "Cancel", "İptal");
            AddTranslation("Delete", "Delete", "Sil");
            AddTranslation("Edit", "Edit", "Düzenle");
            AddTranslation("Add", "Add", "Ekle");
            AddTranslation("Search", "Search", "Ara");
            AddTranslation("Filter", "Filter", "Filtrele");
            AddTranslation("Clear", "Clear", "Temizle");
            AddTranslation("Apply", "Apply", "Uygula");
            AddTranslation("Close", "Close", "Kapat");
            AddTranslation("Yes", "Yes", "Evet");
            AddTranslation("No", "No", "Hayır");
            AddTranslation("OK", "OK", "Tamam");

            // Dashboard
            AddTranslation("WelcomeBack", "Welcome back", "Tekrar hoş geldiniz");
            AddTranslation("ContinueLearning", "Continue your English learning journey and make progress today!", 
                          "İngilizce öğrenme yolculuğunuzda bugün de ilerleme kaydedin!");
            AddTranslation("TotalCourses", "Total Courses", "Toplam Kurs");
            AddTranslation("Completed", "Completed", "Tamamlanan");
            AddTranslation("WordsLearned", "Words Learned", "Öğrenilen Kelime");
            AddTranslation("StudyTime", "Study Time", "Çalışma Süresi");
            AddTranslation("QuickActions", "Quick Actions", "Hızlı İşlemler");
            AddTranslation("ViewCourses", "View Courses", "Kursları Görüntüle");
            AddTranslation("ContinueLearningAction", "Continue Learning", "Öğrenmeye Devam Et");
            AddTranslation("ViewProgress", "View Progress", "İlerlemeyi Gör");
            AddTranslation("RecentActivities", "Recent Activities", "Son Aktiviteler");

            // Courses
            AddTranslation("EnglishCourses", "English Courses", "İngilizce Kurslar");
            AddTranslation("ChooseCourses", "Choose courses that match your level and start your English learning journey!", 
                          "Seviyenize uygun kursları seçin ve İngilizce öğrenme yolculuğunuza başlayın!");
            AddTranslation("FilterCourses", "Filter Courses", "Kursları Filtrele");
            AddTranslation("SelectLevel", "Select Level", "Seviye Seçin");
            AddTranslation("SelectType", "Select Type", "Tür Seçin");
            AddTranslation("ApplyFilter", "Apply Filter", "Filtreyi Uygula");
            AddTranslation("ViewDetails", "View Details", "Detayları Gör");
            AddTranslation("Enroll", "Enroll", "Kayıt Ol");
            AddTranslation("AllLevels", "All Levels", "Tüm Seviyeler");
            AddTranslation("Beginner", "Beginner", "Başlangıç");
            AddTranslation("Intermediate", "Intermediate", "Orta");
            AddTranslation("Advanced", "Advanced", "İleri");
            AddTranslation("AllTypes", "All Types", "Tüm Türler");
            AddTranslation("Grammar", "Grammar", "Gramer");
            AddTranslation("Speaking", "Speaking", "Konuşma");
            AddTranslation("Listening", "Listening", "Dinleme");
            AddTranslation("Reading", "Reading", "Okuma");
            AddTranslation("Writing", "Writing", "Yazma");
            AddTranslation("Pronunciation", "Pronunciation", "Telaffuz");

            // Vocabulary
            AddTranslation("VocabularyLearning", "Vocabulary Learning", "Kelime Öğrenme");
            AddTranslation("ExpandVocabulary", "Expand your English vocabulary with interactive word learning!", 
                          "Etkileşimli kelime öğrenme ile İngilizce kelime dağarcığınızı genişletin!");
            AddTranslation("StudyMode", "Study Mode", "Çalışma Modu");
            AddTranslation("Flashcards", "Flashcards", "Kartlar");
            AddTranslation("QuizMode", "Quiz Mode", "Quiz Modu");
            AddTranslation("ReviewWords", "Review Words", "Kelimeleri Gözden Geçir");
            AddTranslation("Categories", "Categories", "Kategoriler");
            AddTranslation("YourProgress", "Your Progress", "İlerlemeniz");
            AddTranslation("TotalWords", "Total Words", "Toplam Kelime");
            AddTranslation("Learned", "Learned", "Öğrenilen");
            AddTranslation("NeedReview", "Need Review", "Gözden Geçirilmeli");
            AddTranslation("Accuracy", "Accuracy", "Doğruluk");
            AddTranslation("RecentWords", "Recent Words", "Son Kelimeler");

            // Achievements
            AddTranslation("TrackProgress", "Track your learning progress and unlock achievements!", 
                          "Öğrenme ilerlemenizi takip edin ve başarıları açın!");
            AddTranslation("Earned", "Earned", "Kazanılan");
            AddTranslation("TotalPoints", "Total Points", "Toplam Puan");
            AddTranslation("Completion", "Completion", "Tamamlanma");
            AddTranslation("InProgress", "In Progress", "Devam Ediyor");

            // Levels and difficulties
            AddTranslation("Easy", "Easy", "Kolay");
            AddTranslation("Medium", "Medium", "Orta");
            AddTranslation("Hard", "Hard", "Zor");

            // Time formats
            AddTranslation("JustNow", "Just now", "Az önce");
            AddTranslation("MinutesAgo", "minutes ago", "dakika önce");
            AddTranslation("HoursAgo", "hours ago", "saat önce");
            AddTranslation("DaysAgo", "days ago", "gün önce");

            // Messages
            AddTranslation("NoCoursesFound", "No courses found", "Kurs bulunamadı");
            AddTranslation("AdjustFilters", "Try adjusting your filters or check back later for new courses.", 
                          "Filtrelerinizi ayarlayın veya yeni kurslar için daha sonra tekrar kontrol edin.");
            AddTranslation("NoAchievementsFound", "No achievements found", "Başarı bulunamadı");
            AddTranslation("StartLearning", "Start learning to unlock your first achievement!", 
                          "İlk başarınızı açmak için öğrenmeye başlayın!");
            AddTranslation("ErrorLoadingData", "Error loading data", "Veri yüklenirken hata oluştu");
            AddTranslation("StartJourney", "Start your learning journey!", "Öğrenme yolculuğunuza başlayın!");
            AddTranslation("PurchaseFirstCourse", "Begin by purchasing your first course", 
                          "İlk kursu satın alarak başlayın");
        }

        private static void AddTranslation(string key, string english, string turkish)
        {
            _translations[key] = new Dictionary<SupportedLanguage, string>
            {
                { SupportedLanguage.English, english },
                { SupportedLanguage.Turkish, turkish }
            };
        }

        public static string GetText(string key)
        {
            if (_translations.TryGetValue(key, out var translations))
            {
                if (translations.TryGetValue(_currentLanguage, out var translation))
                {
                    return translation;
                }
            }
            return key; // Fallback to key if translation not found
        }

        public static string GetFormattedText(string key, params object[] args)
        {
            var text = GetText(key);
            return string.Format(text, args);
        }

        public static void SetLanguage(SupportedLanguage language)
        {
            CurrentLanguage = language;
        }

        public static string GetLanguageDisplayName(SupportedLanguage language)
        {
            return language switch
            {
                SupportedLanguage.English => "English",
                SupportedLanguage.Turkish => "Türkçe",
                _ => language.ToString()
            };
        }

        public static List<SupportedLanguage> GetSupportedLanguages()
        {
            return Enum.GetValues<SupportedLanguage>().ToList();
        }
    }
}
