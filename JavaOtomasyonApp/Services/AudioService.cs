using System.Diagnostics;
using System.Runtime.InteropServices;

namespace JavaOtomasyonApp.Services
{
    public static class AudioService
    {
        private static readonly Dictionary<string, string> _pronunciationCache = new();

        static AudioService()
        {
            InitializePronunciationCache();
        }

        private static void InitializePronunciationCache()
        {
            // Temel kelimeler için telaffuz URL'leri (gerçek uygulamada API'den gelecek)
            _pronunciationCache["hello"] = "https://ssl.gstatic.com/dictionary/static/sounds/20220808/hello--_gb_1.mp3";
            _pronunciationCache["mother"] = "https://ssl.gstatic.com/dictionary/static/sounds/20220808/mother--_gb_1.mp3";
            _pronunciationCache["father"] = "https://ssl.gstatic.com/dictionary/static/sounds/20220808/father--_gb_1.mp3";
            _pronunciationCache["brother"] = "https://ssl.gstatic.com/dictionary/static/sounds/20220808/brother--_gb_1.mp3";
            _pronunciationCache["sister"] = "https://ssl.gstatic.com/dictionary/static/sounds/20220808/sister--_gb_1.mp3";
            _pronunciationCache["red"] = "https://ssl.gstatic.com/dictionary/static/sounds/20220808/red--_gb_1.mp3";
            _pronunciationCache["blue"] = "https://ssl.gstatic.com/dictionary/static/sounds/20220808/blue--_gb_1.mp3";
            _pronunciationCache["green"] = "https://ssl.gstatic.com/dictionary/static/sounds/20220808/green--_gb_1.mp3";
            _pronunciationCache["apple"] = "https://ssl.gstatic.com/dictionary/static/sounds/20220808/apple--_gb_1.mp3";
            _pronunciationCache["bread"] = "https://ssl.gstatic.com/dictionary/static/sounds/20220808/bread--_gb_1.mp3";
            _pronunciationCache["water"] = "https://ssl.gstatic.com/dictionary/static/sounds/20220808/water--_gb_1.mp3";
        }

        public static async Task PlayPronunciationAsync(string word)
        {
            try
            {
                var normalizedWord = word.ToLowerInvariant().Trim();
                
                if (_pronunciationCache.TryGetValue(normalizedWord, out var audioUrl))
                {
                    await PlayAudioFromUrlAsync(audioUrl);
                }
                else
                {
                    // Fallback: Text-to-Speech kullan
                    await PlayTextToSpeechAsync(word);
                }
            }
            catch (Exception ex)
            {
                // Hata durumunda sessizce devam et
                Console.WriteLine($"Audio playback error: {ex.Message}");
            }
        }

        private static async Task PlayAudioFromUrlAsync(string audioUrl)
        {
            try
            {
                // Platform-specific audio playback
                if (RuntimeInformation.IsOSPlatform(OSPlatform.Windows))
                {
                    await PlayAudioWindowsAsync(audioUrl);
                }
                else if (RuntimeInformation.IsOSPlatform(OSPlatform.OSX))
                {
                    await PlayAudioMacAsync(audioUrl);
                }
                else if (RuntimeInformation.IsOSPlatform(OSPlatform.Linux))
                {
                    await PlayAudioLinuxAsync(audioUrl);
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Platform-specific audio error: {ex.Message}");
            }
        }

        private static async Task PlayAudioWindowsAsync(string audioUrl)
        {
            try
            {
                // Windows'ta varsayılan medya oynatıcısı ile çal
                var startInfo = new ProcessStartInfo
                {
                    FileName = "cmd",
                    Arguments = $"/c start \"\" \"{audioUrl}\"",
                    UseShellExecute = false,
                    CreateNoWindow = true
                };

                using var process = Process.Start(startInfo);
                if (process != null)
                {
                    await process.WaitForExitAsync();
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Windows audio error: {ex.Message}");
            }
        }

        private static async Task PlayAudioMacAsync(string audioUrl)
        {
            try
            {
                // macOS'ta afplay komutu ile çal
                var startInfo = new ProcessStartInfo
                {
                    FileName = "afplay",
                    Arguments = $"\"{audioUrl}\"",
                    UseShellExecute = false,
                    CreateNoWindow = true
                };

                using var process = Process.Start(startInfo);
                if (process != null)
                {
                    await process.WaitForExitAsync();
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"macOS audio error: {ex.Message}");
                // Fallback: open komutu ile varsayılan uygulamada aç
                await PlayAudioMacFallbackAsync(audioUrl);
            }
        }

        private static async Task PlayAudioMacFallbackAsync(string audioUrl)
        {
            try
            {
                var startInfo = new ProcessStartInfo
                {
                    FileName = "open",
                    Arguments = $"\"{audioUrl}\"",
                    UseShellExecute = false,
                    CreateNoWindow = true
                };

                using var process = Process.Start(startInfo);
                if (process != null)
                {
                    await process.WaitForExitAsync();
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"macOS fallback audio error: {ex.Message}");
            }
        }

        private static async Task PlayAudioLinuxAsync(string audioUrl)
        {
            try
            {
                // Linux'ta xdg-open ile varsayılan uygulamada aç
                var startInfo = new ProcessStartInfo
                {
                    FileName = "xdg-open",
                    Arguments = $"\"{audioUrl}\"",
                    UseShellExecute = false,
                    CreateNoWindow = true
                };

                using var process = Process.Start(startInfo);
                if (process != null)
                {
                    await process.WaitForExitAsync();
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Linux audio error: {ex.Message}");
            }
        }

        private static async Task PlayTextToSpeechAsync(string text)
        {
            try
            {
                // Platform-specific TTS
                if (RuntimeInformation.IsOSPlatform(OSPlatform.Windows))
                {
                    await PlayTTSWindowsAsync(text);
                }
                else if (RuntimeInformation.IsOSPlatform(OSPlatform.OSX))
                {
                    await PlayTTSMacAsync(text);
                }
                else if (RuntimeInformation.IsOSPlatform(OSPlatform.Linux))
                {
                    await PlayTTSLinuxAsync(text);
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"TTS error: {ex.Message}");
            }
        }

        private static async Task PlayTTSWindowsAsync(string text)
        {
            try
            {
                // Windows SAPI kullan
                var startInfo = new ProcessStartInfo
                {
                    FileName = "powershell",
                    Arguments = $"-Command \"Add-Type -AssemblyName System.Speech; (New-Object System.Speech.Synthesis.SpeechSynthesizer).Speak('{text}')\"",
                    UseShellExecute = false,
                    CreateNoWindow = true
                };

                using var process = Process.Start(startInfo);
                if (process != null)
                {
                    await process.WaitForExitAsync();
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Windows TTS error: {ex.Message}");
            }
        }

        private static async Task PlayTTSMacAsync(string text)
        {
            try
            {
                // macOS'ta say komutu kullan
                var startInfo = new ProcessStartInfo
                {
                    FileName = "say",
                    Arguments = $"\"{text}\"",
                    UseShellExecute = false,
                    CreateNoWindow = true
                };

                using var process = Process.Start(startInfo);
                if (process != null)
                {
                    await process.WaitForExitAsync();
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"macOS TTS error: {ex.Message}");
            }
        }

        private static async Task PlayTTSLinuxAsync(string text)
        {
            try
            {
                // Linux'ta espeak kullan (yüklü ise)
                var startInfo = new ProcessStartInfo
                {
                    FileName = "espeak",
                    Arguments = $"\"{text}\"",
                    UseShellExecute = false,
                    CreateNoWindow = true
                };

                using var process = Process.Start(startInfo);
                if (process != null)
                {
                    await process.WaitForExitAsync();
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Linux TTS error: {ex.Message}");
            }
        }

        public static bool HasPronunciation(string word)
        {
            var normalizedWord = word.ToLowerInvariant().Trim();
            return _pronunciationCache.ContainsKey(normalizedWord);
        }

        public static string GetPronunciationUrl(string word)
        {
            var normalizedWord = word.ToLowerInvariant().Trim();
            return _pronunciationCache.TryGetValue(normalizedWord, out var url) ? url : string.Empty;
        }

        public static void AddPronunciation(string word, string audioUrl)
        {
            var normalizedWord = word.ToLowerInvariant().Trim();
            _pronunciationCache[normalizedWord] = audioUrl;
        }

        public static List<string> GetAvailablePronunciations()
        {
            return _pronunciationCache.Keys.ToList();
        }
    }
}
