# English Automation Platform 🌟

This project is a modern and comprehensive desktop application developed for those who want to learn English automation. It is developed using C# Windows Forms technology and provides a user-friendly interface.

## 🚀 Features

### ✅ Functional Requirements

#### 1. User Management
- ✅ User registration system (email registration/password setting)
- ✅ Login (authentication)
- ✅ Password reset mechanism
- ✅ Profile management
- ✅ Multi-language support (Turkish/English)

#### 2. Modern UI/UX
- ✅ Modern Windows Forms design
- ✅ Card-based layout
- ✅ Responsive design
- ✅ User-friendly interface

#### 3. English Automation Learning System
- ✅ Level-based courses (Beginner, Intermediate, Advanced)
- ✅ Course types (Grammar, Vocabulary, Speaking, Listening, Reading, Writing, Pronunciation)
- ✅ Interactive lessons and quizzes
- ✅ Vocabulary learning system
- ✅ Pronunciation practice
- ✅ Progress tracking

#### 4. <PERSON><PERSON><PERSON>ren<PERSON> (Vocabulary)
- ✅ Kategorize edilmiş kelime listeleri
- ✅ Flashcard sistemi
- ✅ Quiz modu
- ✅ Kelime hakimiyet seviyesi takibi
- ✅ Tekrar sistemi
- ✅ Telaffuz ses dosyaları

#### 5. <PERSON><PERSON><PERSON><PERSON>stemi (Achievements)
- ✅ Kurs tamamlama rozetleri
- ✅ Kelime öğrenme başarıları
- ✅ Çalışma serisi takibi
- ✅ Puan sistemi
- ✅ İlerleme yüzdeleri

#### 6. Admin Paneli
- ✅ Kurs ekleme, silme ve güncelleme
- ✅ Ders içeriği yönetimi
- ✅ Kelime veritabanı yönetimi
- ✅ Kullanıcı yönetimi
- ✅ İstatistik raporları

#### 7. Ödeme Sistemi
- ✅ Kurs satın alma sistemi (simülasyon)
- ✅ Ödeme geçmişi
- ✅ Ödeme onayı sonrası erişim
- ✅ Ödeme geçmişinin kullanıcıya gösterilmesi
- ✅ İşlem numarası ve detaylı ödeme bilgileri

#### 8. Ana Sayfa / Dashboard
- ✅ Kullanıcının ilerlemesini gösteren modern panel
- ✅ Kurs tamamlama istatistikleri
- ✅ Kelime öğrenme istatistikleri
- ✅ Son aktiviteler listesi
- ✅ Hızlı erişim butonları
- ✅ Motivasyonel mesajlar

#### 9. İlerleme Takibi
- ✅ Detaylı ilerleme raporları
- ✅ Ders ve kelime bazlı takip
- ✅ Çalışma süresi analizi
- ✅ Başarı grafikleri
- ✅ Hedef belirleme sistemi

### ⚙️ Teknik Özellikler

#### Uygulama Özellikleri
- ✅ C# Avalonia UI cross-platform masaüstü uygulaması
- ✅ Modern gradient tasarım ve animasyonlar
- ✅ Offline çalışabilir (SQLite veritabanı)
- ✅ Responsive ve adaptive tasarım
- ✅ Cross-platform desteği (Windows, macOS, Linux)

#### Veritabanı
- ✅ SQLite veritabanı (LocalApplicationData klasöründe)
- ✅ Entity Framework Core kullanımı
- ✅ Kapsamlı veri modeli (User, Course, Lesson, Quiz, Vocabulary, Achievement)
- ✅ Otomatik veritabanı oluşturma ve seed data
- ✅ İlişkisel veri yapısı

#### Güvenlik
- ✅ Şifreler BCrypt ile hashlenmiş
- ✅ Yetkilendirme: Admin ve Normal Kullanıcı rolleri
- ✅ Güvenli authentication sistemi
- ✅ Session yönetimi

## 📁 Proje Yapısı

```
JavaOtomasyonApp/
├── Data/
│   └── AppDbContext.cs          # Entity Framework DbContext
├── Models/
│   ├── User.cs                  # Kullanıcı modeli
│   ├── Course.cs                # Ders modeli
│   ├── Payment.cs               # Ödeme modeli
│   └── UserProgress.cs          # Kullanıcı ilerleme modeli
├── Services/
│   └── AuthenticationService.cs # Kimlik doğrulama servisi
├── Views/
│   ├── LoginWindow.xaml         # Giriş ekranı
│   ├── RegisterWindow.xaml      # Kayıt ekranı
│   ├── MainWindow.xaml          # Ana pencere
│   ├── Pages/
│   │   ├── DashboardPage.xaml   # Dashboard sayfası
│   │   ├── CoursesPage.xaml     # Dersler sayfası
│   │   ├── ProgressPage.xaml    # İlerleme sayfası
│   │   ├── PaymentsPage.xaml    # Ödemeler sayfası
│   │   └── AdminPage.xaml       # Admin paneli
│   └── Windows/
│       ├── CourseDetailWindow.xaml # Ders detay penceresi
│       └── ProfileWindow.xaml   # Profil ayarları penceresi
├── App.xaml                     # Uygulama kaynakları ve stiller
└── JavaOtomasyonApp.csproj      # Proje dosyası
```

## 🛠️ Kurulum ve Çalıştırma

### Gereksinimler
- .NET 6.0 SDK veya üzeri
- Windows 10/11 (WPF uygulaması)

### Kurulum Adımları

1. **Projeyi klonlayın:**
   ```bash
   git clone <repository-url>
   cd java-otomasyon
   ```

2. **NuGet paketlerini yükleyin:**
   ```bash
   dotnet restore JavaOtomasyonApp/JavaOtomasyonApp.csproj
   ```

3. **Uygulamayı çalıştırın:**
   ```bash
   dotnet run --project JavaOtomasyonApp/JavaOtomasyonApp.csproj
   ```

### İlk Çalıştırma

Uygulama ilk çalıştırıldığında:
- SQLite veritabanı otomatik olarak oluşturulur
- Örnek dersler eklenir
- Varsayılan admin kullanıcısı oluşturulur:
  - **E-posta:** <EMAIL>
  - **Şifre:** admin123

## 👤 Kullanım

### Normal Kullanıcı İşlemleri
1. **Kayıt Olma:** Yeni hesap oluşturun
2. **Giriş Yapma:** E-posta ve şifre ile giriş yapın
3. **Dersler:** Mevcut dersleri görüntüleyin ve satın alın
4. **İlerleme:** Ders ilerlemelerinizi takip edin
5. **Ödemeler:** Ödeme geçmişinizi görüntüleyin
6. **Profil:** Kişisel bilgilerinizi güncelleyin

### Admin İşlemleri
1. **Admin Girişi:** <EMAIL> / admin123
2. **Ders Yönetimi:** Yeni ders ekleyin, mevcut dersleri düzenleyin
3. **Kullanıcı Yönetimi:** Tüm kullanıcıları görüntüleyin
4. **Raporlar:** Sistem istatistiklerini ve raporları görüntüleyin

## 🔧 Geliştirme

### Kullanılan Teknolojiler
- **C# 10.0**
- **WPF (Windows Presentation Foundation)**
- **Entity Framework Core 6.0**
- **SQLite**
- **BCrypt.Net** (Şifre hashleme)

### Veritabanı Şeması
- **Users:** Kullanıcı bilgileri
- **Courses:** Ders bilgileri
- **Payments:** Ödeme kayıtları
- **UserProgresses:** Kullanıcı ders ilerlemeleri

## 📝 Notlar

- Bu uygulama eğitim amaçlı geliştirilmiştir
- Ödeme sistemi simülasyon amaçlıdır, gerçek ödeme işlemi yapılmaz
- Veritabanı dosyası `%LocalAppData%\JavaOtomasyon\database.db` konumunda saklanır
- Uygulama offline çalışır, internet bağlantısı gerektirmez

## 🤝 Katkıda Bulunma

1. Fork yapın
2. Feature branch oluşturun (`git checkout -b feature/AmazingFeature`)
3. Değişikliklerinizi commit edin (`git commit -m 'Add some AmazingFeature'`)
4. Branch'inizi push edin (`git push origin feature/AmazingFeature`)
5. Pull Request oluşturun

## 📄 Lisans

Bu proje MIT lisansı altında lisanslanmıştır.
